/* Logo Energy Animations */
.logo-energy-rings {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    transform-origin: center;
    mix-blend-mode: screen;
}

.energy-ring {
    position: absolute;
    border-radius: 50%;
    border: 2px solid transparent;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 
        0 0 15px var(--primary),
        inset 0 0 15px var(--accent);
    background: linear-gradient(45deg,
        rgba(var(--primary-rgb), 0.1),
        rgba(var(--accent-rgb), 0.1));
}

/* Individual Ring Styles */
.ring-1 { 
    width: 100%; 
    height: 100%; 
    border-top: 2px solid var(--primary);
    border-left: 2px solid var(--accent);
    animation: rotateRing 4s linear infinite;
}

.ring-2 { 
    width: 80%; 
    height: 80%; 
    border-right: 2px solid var(--primary);
    border-bottom: 2px solid var(--accent);
    animation: rotateRing 3s linear infinite reverse;
}

.ring-3 { 
    width: 60%; 
    height: 60%; 
    border-top: 2px solid var(--accent);
    border-bottom: 2px solid var(--primary);
    animation: rotateRing 2s linear infinite;
}

.ring-4 { 
    width: 40%; 
    height: 40%;
    border-left: 2px solid var(--accent);
    border-right: 2px solid var(--primary);
    animation: rotateRing 1.5s linear infinite reverse;
}

.ring-5 { 
    width: 20%; 
    height: 20%;
    border: 2px solid var(--primary);
    animation: pulseRing 1s ease-in-out infinite;
}

/* Energy Effects */
.energy-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.energy-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, 
        rgba(var(--primary-rgb), 0.3) 0%, 
        rgba(var(--accent-rgb), 0.1) 30%,
        transparent 70%);
    filter: blur(8px);
    animation: pulseGlow 2s ease-in-out infinite;
}

.energy-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 30% 30%, 
            rgba(var(--primary-rgb), 0.2) 0%, 
            transparent 10%),
        radial-gradient(circle at 70% 70%, 
            rgba(var(--accent-rgb), 0.2) 0%, 
            transparent 10%);
    filter: blur(1px);
    animation: particleFloat 3s ease-in-out infinite;
}

/* Electric Effect */
.electric-effect {
    position: absolute;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 50% 50%,
            rgba(var(--primary-rgb), 0.2) 0%,
            transparent 50%),
        repeating-radial-gradient(circle at 50% 50%,
            rgba(var(--accent-rgb), 0.1) 0%,
            transparent 20%,
            rgba(var(--primary-rgb), 0.1) 40%);
    filter: blur(1px);
    mix-blend-mode: screen;
    animation: electricPulse 3s ease-in-out infinite;
}

/* Animation Keyframes */
@keyframes rotateRing {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes pulseRing {
    0%, 100% { 
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 0 15px var(--primary);
    }
    50% { 
        transform: translate(-50%, -50%) scale(1.2);
        box-shadow: 0 0 30px var(--accent);
    }
}

@keyframes pulseGlow {
    0%, 100% { 
        opacity: 0.5; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.8; 
        transform: scale(1.1);
    }
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
        opacity: 0.8;
    }
}

@keyframes electricPulse {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1) rotate(0deg);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1) rotate(180deg);
    }
}