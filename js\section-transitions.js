document.addEventListener('DOMContentLoaded', () => {
    const heroSection = document.querySelector('#hero');
    const aboutSection = document.querySelector('#about');
    
    // اگر المان‌ها وجود نداشتند، اجرا نشود
    if (!heroSection || !aboutSection) return;
    
    let isTransitioning = false;
    let currentSection = 'hero';

    // Initialize transition elements
    const transitionLayer = aboutSection.querySelector('.transition-layer');
    const floatingElements = aboutSection.querySelectorAll('.floating-elements > div');
    const connectionLines = aboutSection.querySelector('.connection-lines');
    
    // Scroll handler
    window.addEventListener('wheel', (e) => {
        if (isTransitioning) return;
        
        const scrollingDown = e.deltaY > 0;
        const threshold = window.innerHeight * 0.3;
        
        if (scrollingDown && currentSection === 'hero' && window.scrollY > threshold) {
            transitionToAbout();
        } else if (!scrollingDown && currentSection === 'about' && window.scrollY < threshold) {
            transitionToHero();
        }
    });

    function transitionToAbout() {
        isTransitioning = true;
        currentSection = 'about';

        // Main transition animation
        const timeline = anime.timeline({
            easing: 'easeInOutExpo',
            duration: 1200
        });

        timeline
            // Transition layer animation
            .add({
                targets: transitionLayer,
                scaleY: [0, 1],
                duration: 800,
                easing: 'easeInOutCubic'
            })
            // Floating elements entrance
            .add({
                targets: floatingElements,
                opacity: [0, 1],
                translateY: [-50, 0],
                translateX: [-30, 0],
                rotate: [-30, 0],
                delay: anime.stagger(100),
                duration: 1200
            }, '-=400')
            // Connection lines
            .add({
                targets: connectionLines,
                opacity: [0, 0.3],
                scale: [0.9, 1],
                duration: 800
            }, '-=800')
            // Content reveal
            .add({
                targets: '.reveal-content',
                opacity: [0, 1],
                translateY: [50, 0],
                duration: 1000
            }, '-=600')
            // Cards reveal
            .add({
                targets: '.reveal-card',
                opacity: [0, 1],
                translateY: [30, 0],
                delay: anime.stagger(150),
                duration: 800
            }, '-=800')
            // Globe reveal
            .add({
                targets: '.reveal-globe',
                opacity: [0, 1],
                scale: [0.8, 1],
                duration: 1200,
                complete: () => {
                    isTransitioning = false;
                }
            }, '-=600');

        // Floating animation for elements
        floatingElements.forEach(element => {
            anime({
                targets: element,
                translateY: ['+=20', '-=20'],
                translateX: ['+=15', '-=15'],
                rotate: ['+=5', '-=5'],
                duration: 4000,
                direction: 'alternate',
                loop: true,
                easing: 'easeInOutQuad'
            });
        });
    }

    function transitionToHero() {
        isTransitioning = true;
        currentSection = 'hero';

        const timeline = anime.timeline({
            easing: 'easeInOutExpo',
            duration: 1000
        });

        timeline
            // Fade out about section content
            .add({
                targets: ['.reveal-content', '.reveal-card', '.reveal-globe', floatingElements, connectionLines],
                opacity: 0,
                translateY: [0, -30],
                scale: 0.9,
                duration: 600
            })
            // Transition layer animation
            .add({
                targets: transitionLayer,
                scaleY: [1, 0],
                duration: 800,
                easing: 'easeInOutCubic',
                complete: () => {
                    isTransitioning = false;
                }
            }, '-=300');
    }

    // Handle scroll-based reveal animations
    const observerOptions = {
        threshold: 0.3,
        rootMargin: '0px'
    };

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !isTransitioning) {
                if (entry.target.id === 'about') {
                    transitionToAbout();
                }
            }
        });
    }, observerOptions);

    sectionObserver.observe(aboutSection);
});
