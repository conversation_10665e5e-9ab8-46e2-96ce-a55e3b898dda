# INOVA Energy Project

## Overview
The INOVA Energy project is a dynamic and visually appealing website focused on sustainable energy solutions. It features various sections that provide information about the company, its activities, and its rebranding efforts.

## Project Structure
The project is organized into several directories, each serving a specific purpose:

- **components/**: Contains PHP files for different sections of the website.
  - `about.php`: HTML structure and content for the "About" section.
  - `activities.php`: HTML structure and content for the "Activities" section.
  - `header.php`: HTML structure for the website header.
  - `hero.php`: HTML structure and content for the "Hero" section.
  - `rebranding.php`: HTML structure and content for the "Rebranding" section.

- **css/**: Contains stylesheets for the website.
  - **base/**: Base styles for typography, colors, and layout.
  - **components/**: Styles specific to components like animations and headers.
  - **sections/**: Styles specific to each section of the website, including unified styles for smooth transitions.

- **js/**: Contains JavaScript files for animations, interactions, and functionality across the website.

- **index.php**: The main entry point for the website, which includes all components and scripts.

## Features
- Responsive design to ensure compatibility across various devices.
- Smooth transitions between sections for a seamless user experience.
- Dynamic animations to enhance visual appeal and engagement.

## Getting Started
To run the project locally:
1. Clone the repository or download the project files.
2. Set up a local server environment (e.g., WAMP, XAMPP).
3. Place the project files in the server's root directory.
4. Access the project via a web browser at `http://localhost/inova2/index.php`.

## Contributing
Contributions are welcome! Please feel free to submit issues or pull requests to improve the project.

## License
This project is licensed under the MIT License. See the LICENSE file for more details.