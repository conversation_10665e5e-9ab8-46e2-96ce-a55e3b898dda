/* ===================================
   DESKTOP RESPONSIVE OPTIMIZATION
   Professional responsive design for all sections
   =================================== */

/* Desktop Base Styles */
@media (min-width: 1024px) {

    /* Container Optimization */
    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .activities-container,
    .projects-container-professional,
    .rebranding-container,
    .contact-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 40px;
        box-sizing: border-box;
        width: 100%;
    }

    /* Hero Section Desktop */
    .hero-section-professional {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-container-professional {
        display: grid;
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
        align-items: center;
    }

    .hero-title-professional {
        font-size: clamp(3rem, 5vw, 4.5rem);
        line-height: 1.1;
        margin-bottom: 30px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 40px;
        max-width: 600px;
        margin: 0 auto;
    }

    .cta-buttons-container {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    /* About Section Desktop */
    .about-section-ultimate {
        padding: 120px 0;
    }

    .about-content-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        align-items: start;
    }

    .story-card,
    .values-card {
        padding: 40px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .about-statistics {
        margin-top: 80px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 40px;
    }

    /* Activities Section Desktop */
    .activities-section-fixed {
        padding: 120px 0;
    }

    .activities-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 40px;
        margin: 60px 0;
    }

    .activity-card {
        padding: 40px;
        height: 100%;
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
    }

    .activity-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 60px rgba(0, 163, 255, 0.2);
    }

    /* Rebranding Section Desktop */
    .rebranding-section-ultimate {
        padding: 120px 0;
    }

    .rebranding-content-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        align-items: start;
    }

    .benefits-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .benefit-item {
        padding: 30px;
        display: flex;
        gap: 20px;
        align-items: flex-start;
    }

    /* Contact Section Desktop */
    .contact-section-fixed {
        padding: 120px 0;
    }

    .contact-content {
        display: grid;
        grid-template-columns: 1fr 1.2fr;
        gap: 60px;
        align-items: start;
    }

    .info-card,
    .form-card {
        padding: 40px;
        height: 100%;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .form-group.full-width {
        grid-column: 1 / -1;
    }

    /* Typography Optimization */
    .section-title,
    .section-title-ultimate {
        font-size: clamp(2.5rem, 4vw, 3.5rem);
        line-height: 1.2;
        margin-bottom: 30px;
    }

    .title-main {
        font-size: clamp(2.5rem, 4vw, 3.5rem);
    }

    .title-sub {
        font-size: clamp(1.5rem, 2.5vw, 2rem);
    }

    .section-description,
    .section-description-ultimate {
        font-size: 1.2rem;
        line-height: 1.6;
        max-width: 800px;
        margin: 0 auto 50px;
    }

    /* Button Optimization */
    .cta-primary,
    .cta-secondary,
    .cta-primary-advanced,
    .cta-secondary-advanced {
        padding: 16px 32px;
        font-size: 1.1rem;
        min-width: 200px;
        justify-content: center;
    }

    /* Card Hover Effects */
    .story-card:hover,
    .values-card:hover,
    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 163, 255, 0.15);
    }

    /* Grid Responsive Adjustments */
    @media (min-width: 1200px) {
        .activities-grid {
            grid-template-columns: repeat(3, 1fr);
        }

        .benefits-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (min-width: 1400px) {

        .hero-container-professional,
        .about-container,
        .activities-container,
        .rebranding-container,
        .contact-container {
            max-width: 1600px;
            padding: 0 60px;
        }

        .activities-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 50px;
        }

        .about-content-grid,
        .rebranding-content-grid {
            gap: 80px;
        }

        .contact-content {
            gap: 80px;
        }
    }

    /* Ultra-wide Screens */
    @media (min-width: 1800px) {

        .hero-container-professional,
        .about-container,
        .activities-container,
        .rebranding-container,
        .contact-container {
            max-width: 1800px;
            padding: 0 80px;
        }

        .hero-title-professional {
            font-size: 5rem;
        }

        .section-title,
        .section-title-ultimate {
            font-size: 4rem;
        }

        .section-description,
        .section-description-ultimate {
            font-size: 1.3rem;
            max-width: 900px;
        }
    }
}

/* Large Desktop Specific */
@media (min-width: 1024px) and (max-width: 1399px) {
    .activities-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .about-content-grid,
    .rebranding-content-grid {
        gap: 40px;
    }

    .contact-content {
        gap: 40px;
    }
}

/* Performance Optimizations for Desktop */
@media (min-width: 1024px) {

    /* Enable hardware acceleration for smooth animations */
    .activity-card,
    .story-card,
    .values-card,
    .info-card,
    .form-card,
    .benefit-item {
        will-change: transform;
        backface-visibility: hidden;
        transform: translateZ(0);
    }

    /* Optimize scroll performance */
    .hero-section-professional,
    .about-section-ultimate,
    .activities-section-fixed,
    .rebranding-section-ultimate,
    .contact-section-fixed {
        contain: layout style paint;
    }

    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
    }

    /* Optimize images */
    img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* High DPI Displays */
@media (min-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {

    .hero-logo-main,
    .site-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    /* Sharper text rendering */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}

/* Focus and Accessibility */
@media (min-width: 1024px) {

    /* Better focus indicators */
    .cta-primary:focus,
    .cta-secondary:focus,
    .cta-primary-advanced:focus,
    .cta-secondary-advanced:focus,
    .lang-toggle:focus {
        outline: 2px solid var(--accent);
        outline-offset: 2px;
    }

    /* Keyboard navigation */
    .activity-card:focus-within,
    .story-card:focus-within,
    .values-card:focus-within {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 163, 255, 0.2);
    }
}