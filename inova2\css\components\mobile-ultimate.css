/* ===================================
   INOVA ENERGY MOBILE OPTIMIZATION - ULTIMATE
   Professional Mobile & Tablet Experience
   =================================== */

/* Mobile Performance Optimization */
@media (max-width: 1023px) {
    /* Reduce animations for better performance */
    * {
        animation-duration: 0.3s !important;
        transition-duration: 0.3s !important;
    }
    
    /* Optimize transforms */
    .hero-logo-img,
    .floating-shape,
    .field-ring,
    .particle-burst,
    .glow-orb,
    .shape-element {
        will-change: transform;
        transform: translateZ(0);
        backface-visibility: hidden;
    }
    
    /* Reduce complex animations */
    .particle-field,
    .enhancement-particles {
        display: none;
    }
    
    /* Simplify backgrounds */
    .hero-background .background-layers,
    .about-background {
        opacity: 0.5;
    }
    
    /* Optimize fonts */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeSpeed;
    }
}

/* Touch Optimization */
@media (max-width: 1023px) {
    /* Larger touch targets */
    .cta-primary,
    .cta-secondary,
    .feature-item,
    .value-item,
    .stat-item,
    .goal-item {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* Better touch feedback */
    .cta-primary:active,
    .cta-secondary:active,
    .feature-item:active,
    .value-item:active {
        transform: scale(0.98);
        opacity: 0.8;
    }
    
    /* Remove hover effects on touch devices */
    .feature-item:hover,
    .value-item:hover,
    .stat-item:hover,
    .story-card:hover,
    .values-card:hover,
    .vision-card:hover {
        transform: none;
        box-shadow: none;
    }
    
    /* Optimize scrolling */
    body {
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
    }
}

/* Mobile Typography */
@media (max-width: 767px) {
    /* Responsive font scaling */
    html {
        font-size: 14px;
    }
    
    .hero-title {
        font-size: clamp(1.8rem, 5vw, 2.5rem);
        line-height: 1.2;
    }
    
    .title-main {
        font-size: clamp(1.6rem, 4vw, 2.2rem);
    }
    
    .title-sub {
        font-size: clamp(1rem, 3vw, 1.4rem);
    }
    
    .section-description,
    .hero-description {
        font-size: clamp(0.9rem, 2.5vw, 1.1rem);
        line-height: 1.6;
    }
    
    /* Better text contrast */
    .hero-description,
    .section-description,
    .timeline-content p,
    .value-content p {
        color: rgba(255, 255, 255, 0.9);
    }
}

/* Mobile Layout Optimization */
@media (max-width: 767px) {
    /* Container spacing */
    .hero-container,
    .about-container {
        padding: 0 16px;
    }
    
    /* Section spacing */
    .hero-section,
    .about-section-ultimate {
        padding: 60px 0 40px;
    }
    
    /* Grid optimization */
    .hero-layout,
    .about-content-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    /* Card spacing */
    .story-card,
    .values-card {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    /* Statistics grid */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    /* Vision goals */
    .vision-goals {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* Tablet Optimization */
@media (max-width: 1023px) and (min-width: 768px) {
    /* Tablet-specific layouts */
    .hero-layout {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-logo-section {
        order: 1;
    }
    
    .hero-content-section {
        order: 2;
    }
    
    /* Tablet typography */
    .hero-title {
        font-size: 2.8rem;
    }
    
    .title-main {
        font-size: 2.5rem;
    }
    
    .title-sub {
        font-size: 1.5rem;
    }
    
    /* Tablet spacing */
    .hero-container,
    .about-container {
        padding: 0 30px;
    }
    
    .hero-section,
    .about-section-ultimate {
        padding: 80px 0;
    }
}

/* Mobile Navigation */
@media (max-width: 767px) {
    /* Mobile menu optimization */
    .site-header {
        padding: 15px 20px;
    }
    
    .main-navigation {
        position: fixed;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100vh;
        background: rgba(0, 15, 30, 0.98);
        backdrop-filter: blur(20px);
        transition: left 0.3s ease;
        z-index: 9999;
    }
    
    .main-navigation.active {
        left: 0;
    }
    
    .nav-menu {
        flex-direction: column;
        padding: 80px 20px 20px;
        gap: 20px;
    }
    
    .nav-menu a {
        font-size: 1.1rem;
        padding: 12px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    /* Mobile menu toggle */
    .menu-toggle {
        display: block;
        background: none;
        border: none;
        color: var(--text-light);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 8px;
    }
    
    /* Language switcher mobile */
    .language-switcher {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
    }
}

/* Mobile Forms */
@media (max-width: 767px) {
    /* Form optimization */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    textarea {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-light);
        width: 100%;
        box-sizing: border-box;
    }
    
    input[type="text"]:focus,
    input[type="email"]:focus,
    input[type="tel"]:focus,
    textarea:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(0, 163, 255, 0.2);
    }
    
    /* Button optimization */
    .cta-primary,
    .cta-secondary {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
        padding: 14px 24px;
        font-size: 1rem;
        border-radius: 25px;
    }
}

/* Mobile Images */
@media (max-width: 767px) {
    /* Image optimization */
    .hero-logo-img {
        width: 150px;
        height: 150px;
        object-fit: contain;
    }
    
    /* Lazy loading optimization */
    img {
        loading: lazy;
        decoding: async;
    }
    
    /* Reduce image effects */
    .hero-logo-img {
        filter: drop-shadow(0 0 20px rgba(0, 163, 255, 0.3));
    }
}

/* Mobile Animations Control */
@media (max-width: 767px) {
    /* Reduce motion for better performance */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
    
    /* Battery optimization */
    @media (prefers-reduced-motion: no-preference) {
        .floating-shape,
        .field-ring,
        .shape-element {
            animation-duration: 8s;
            animation-timing-function: ease-out;
        }
        
        .hero-logo-img {
            animation-duration: 4s;
        }
        
        .particle-burst,
        .glow-orb,
        .vision-glow {
            animation-duration: 6s;
        }
    }
}

/* Mobile Accessibility */
@media (max-width: 767px) {
    /* Focus indicators */
    .cta-primary:focus,
    .cta-secondary:focus,
    .feature-item:focus,
    .value-item:focus {
        outline: 2px solid var(--accent);
        outline-offset: 2px;
    }
    
    /* High contrast support */
    @media (prefers-contrast: high) {
        .hero-section,
        .about-section-ultimate {
            background: #000;
        }
        
        .hero-description,
        .section-description,
        .timeline-content p,
        .value-content p {
            color: #fff;
        }
        
        .story-card,
        .values-card,
        .stat-item,
        .vision-card {
            border: 2px solid var(--primary);
            background: rgba(255, 255, 255, 0.1);
        }
    }
    
    /* Large text support */
    @media (prefers-reduced-motion: reduce) {
        body {
            font-size: 18px;
            line-height: 1.6;
        }
        
        .hero-title {
            font-size: 2rem;
        }
        
        .title-main {
            font-size: 1.8rem;
        }
    }
}

/* Mobile Performance */
@media (max-width: 767px) {
    /* GPU acceleration */
    .hero-logo-img,
    .cta-primary,
    .stat-item,
    .story-card,
    .values-card {
        transform: translateZ(0);
        will-change: transform;
    }
    
    /* Optimize repaints */
    .hero-background,
    .about-background {
        contain: layout style paint;
    }
    
    /* Reduce complexity */
    .bg-gradient-primary,
    .gradient-layer {
        background: linear-gradient(135deg, 
            rgba(0, 31, 63, 1) 0%, 
            rgba(0, 15, 30, 1) 100%
        );
    }
}

/* Mobile Landscape */
@media (max-width: 767px) and (orientation: landscape) {
    .hero-section {
        padding: 40px 0 30px;
        min-height: 100vh;
    }
    
    .hero-title {
        font-size: 2rem;
        margin-bottom: 16px;
    }
    
    .hero-description {
        font-size: 1rem;
        margin-bottom: 20px;
    }
    
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }
    
    .hero-stats {
        gap: 20px;
    }
    
    .logo-image {
        width: 120px;
        height: 120px;
    }
}
