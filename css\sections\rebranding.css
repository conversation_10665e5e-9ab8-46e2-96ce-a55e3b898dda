.rebranding-section {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(135deg, #0a0a23 0%, #1a1a4d 100%);
    padding: 5rem 2rem 3rem 2rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Vazirmatn', '<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
    direction: rtl;
}

/* پس‌زمینه‌های دکوراتیو */
.rebranding-bg {
    position: absolute;
    inset: 0;
    z-index: 1;
    pointer-events: none;
}
.animated-gradient {
    position: absolute;
    width: 1200px;
    height: 1200px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle at 60% 40%, #00a3ff33 0%, #1a1a4d00 80%);
    filter: blur(80px);
    opacity: 0.7;
    animation: pulse 8s infinite alternate;
}
.blurred-shape {
    position: absolute;
    width: 600px;
    height: 600px;
    right: -200px;
    bottom: -200px;
    background: radial-gradient(circle, #00a3ff44 0%, #fff0 80%);
    filter: blur(100px);
    opacity: 0.5;
}
.grid-overlay {
    position: absolute;
    inset: 0;
    background-image: 
        linear-gradient(rgba(255,255,255,0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px);
    background-size: 30px 30px;
    transform: perspective(500px) rotateX(60deg);
    animation: gridMove 20s linear infinite;
}

/* --- کانتینر و هدر --- */
.rebranding-container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.rebranding-header {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 2.5rem;
    margin-bottom: 3rem;
}

.header-content {
    flex: 1 1 320px;
    min-width: 220px;
}

.year-badge {
    display: inline-block;
    padding: 0.5rem 1.5rem;
    background: rgba(0,163,255,0.12);
    border: 1px solid rgba(0,163,255,0.25);
    border-radius: 50px;
    color: #00a3ff;
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    letter-spacing: 1px;
}

.section-title {
    font-size: 2.7rem;
    font-weight: 900;
    background: linear-gradient(90deg, #fff 60%, #00a3ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.subtitle {
    color: #b3e6ff;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    font-weight: 400;
}

/* --- برند استیج --- */
.brand-evolution {
    flex: 1 1 320px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    min-width: 220px;
}

.brand-stages {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.stage {
    text-align: center;
}

.stage-year {
    color: #00a3ff;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    display: block;
}

.brand-logo {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    background: linear-gradient(135deg, #00a3ff 60%, #fff 100%);
    box-shadow: 0 4px 24px #00a3ff33;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}
.brand-logo.old { opacity: 0.5; filter: grayscale(1); }
.brand-logo.new { border: 2px solid #00a3ff; }

/* --- محتوای اصلی --- */
.rebranding-content,
.grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
    align-items: stretch;
}

.main-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.info-card {
    padding: 2.2rem 2rem;
    background: rgba(255,255,255,0.05);
    border-radius: 22px;
    box-shadow: 0 2px 24px #00a3ff11;
    border: 1px solid rgba(0,163,255,0.10);
    transition: box-shadow 0.3s, border 0.3s;
    backdrop-filter: blur(2px);
}

.info-card:hover {
    box-shadow: 0 8px 32px #00a3ff33;
    border: 1.5px solid #00a3ff;
}

.info-title {
    font-size: 1.5rem;
    color: #fff;
    font-weight: 700;
    margin-bottom: 1rem;
}

.main-text {
    font-size: 1.13rem;
    line-height: 1.9;
    color: #e6f7ff;
    margin-bottom: 1.5rem;
}

.highlight-box {
    position: relative;
    padding: 1.3rem 1.5rem 1.3rem 1.2rem;
    background: rgba(0,163,255,0.10);
    border-radius: 15px;
    overflow: hidden;
    margin-top: 1rem;
    color: #fff;
    font-size: 1.05rem;
    box-shadow: 0 2px 12px #00a3ff22;
}

.glow-line {
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #00a3ff 0%, #fff0 100%);
    box-shadow: 0 0 16px #00a3ff;
    border-radius: 4px;
}

/* --- دلایل ریبرندینگ --- */
.reasons-container {
    padding: 2rem 1.5rem;
    background: rgba(255,255,255,0.04);
    border-radius: 22px;
    box-shadow: 0 2px 24px #00a3ff11;
    border: 1px solid rgba(0,163,255,0.10);
    display: flex;
    flex-direction: column;
    justify-content: center;
    backdrop-filter: blur(2px);
}

.reasons-title {
    font-size: 1.4rem;
    color: #fff;
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 700;
    letter-spacing: 1px;
}

.reasons-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.reason-card {
    position: relative;
    padding: 1.5rem 1.2rem;
    background: rgba(0,163,255,0.09);
    border-radius: 16px;
    border: 1px solid rgba(0,163,255,0.18);
    box-shadow: 0 2px 12px #00a3ff11;
    transition: transform 0.3s, box-shadow 0.3s, border 0.3s;
    cursor: pointer;
    backdrop-filter: blur(1.5px);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.reason-card:hover {
    transform: translateY(-7px) scale(1.04);
    box-shadow: 0 8px 32px #00a3ff33;
    border: 1.5px solid #00a3ff;
}

.card-icon {
    width: 44px;
    height: 44px;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.card-content h4 {
    color: #00a3ff;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.card-content p {
    color: #e6f7ff;
    font-size: 1rem;
    line-height: 1.7;
}

/* --- افکت‌های انیمیشن --- */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards;
}
.fade-in.delay-1 { animation-delay: 0.4s; }
@keyframes fadeInUp {
    to { opacity: 1; transform: none; }
}
@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.4; }
}
@keyframes gridMove {
    0% { transform: perspective(500px) rotateX(60deg) translateY(0); }
    100% { transform: perspective(500px) rotateX(60deg) translateY(30px); }
}

/* --- ریسپانسیو حرفه‌ای --- */
@media (max-width: 1200px) {
    .rebranding-container {
        max-width: 98vw;
    }
    .rebranding-header {
        gap: 1.5rem;
    }
    .rebranding-content,
    .grid-2 {
        gap: 1.5rem;
    }
}

@media (max-width: 1024px) {
    .rebranding-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
        text-align: right;
    }
    .brand-evolution {
        justify-content: flex-start;
    }
    .rebranding-content,
    .grid-2 {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    .reasons-grid {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .rebranding-section {
        padding: 2.5rem 0.5rem;
    }
    .rebranding-header {
        gap: 1rem;
    }
    .section-title {
        font-size: 2rem;
    }
    .reasons-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    .info-card,
    .reasons-container {
        padding: 1.2rem 0.7rem;
    }
    .brand-logo {
        width: 44px;
        height: 44px;
    }
}

@media (max-width: 480px) {
    .rebranding-section {
        padding: 1.2rem 0;
    }
    .section-title {
        font-size: 1.1rem;
    }
    .brand-logo {
        width: 32px;
        height: 32px;
        border-radius: 8px;
    }
    .info-card,
    .reasons-container {
        padding: 0.7rem 0.3rem;
        border-radius: 10px;
    }
    .highlight-box {
        padding: 0.7rem 0.5rem 0.7rem 0.3rem;
        border-radius: 6px;
    }
    .reason-card {
        padding: 0.7rem 0.3rem;
        border-radius: 6px;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    .reasons-title {
        font-size: 1rem;
    }
}

/* --- ابزارهای کمکی --- */
.flex-between { display: flex; justify-content: space-between; align-items: center; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 2.5rem; }



