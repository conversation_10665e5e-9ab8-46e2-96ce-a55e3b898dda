/**
 * Language Switcher for INOVA Energy Theme
 * Handles switching between Persian (RTL) and English (LTR) layouts
 */

(function ($) {
    'use strict';

    // Language switcher functionality
    window.toggleLanguage = function () {
        const currentLang = document.documentElement.lang === 'en' ? 'en' : 'fa';
        const newLang = currentLang === 'fa' ? 'en' : 'fa';

        // Show loading state
        const langButton = document.querySelector('.lang-toggle') || document.querySelector('.mobile-lang-toggle');
        if (!langButton) {
            console.error('Language button not found');
            return;
        }

        const originalContent = langButton.innerHTML;
        langButton.innerHTML = '<div class="loading-spinner"></div>';
        langButton.disabled = true;

        // Send AJAX request to switch language
        $.ajax({
            url: inova_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'inova_switch_language',
                lang: newLang,
                nonce: inova_ajax.nonce
            },
            success: function (response) {
                if (response.success) {
                    // Update body classes
                    document.body.classList.remove('lang-fa', 'lang-en', 'rtl', 'ltr');
                    document.body.classList.add('lang-' + newLang);
                    document.body.classList.add(newLang === 'fa' ? 'rtl' : 'ltr');

                    // Update HTML direction and language
                    document.documentElement.dir = newLang === 'fa' ? 'rtl' : 'ltr';
                    document.documentElement.lang = newLang;

                    // Update content immediately
                    updateContent(newLang);

                    // Update button text
                    updateLanguageButton(newLang);

                    // Trigger custom event
                    $(document).trigger('languageChanged', [newLang]);

                    // Force reload to apply all changes
                    window.location.reload();
                } else {
                    console.error('Language switch failed:', response.data);
                    langButton.innerHTML = originalContent;
                    langButton.disabled = false;
                }
            },
            error: function (xhr, status, error) {
                console.error('AJAX error:', error);
                langButton.innerHTML = originalContent;
                langButton.disabled = false;
            }
        });
    };

    // Update language button text
    function updateLanguageButton(lang) {
        const langButton = document.querySelector('.lang-switch .lang-text');
        if (langButton) {
            langButton.textContent = lang === 'fa' ? 'EN' : 'فا';
        }
    }

    // Update content based on language
    function updateContent(lang) {
        // Update page title
        const pageTitle = lang === 'fa' ?
            'INOVA Energy | پیشگام در صنعت انرژی پایدار' :
            'INOVA Energy | Leading Sustainable Energy Innovation';
        document.title = pageTitle;

        // Update meta description
        const metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            const description = lang === 'fa' ?
                'گروه بین‌المللی INOVA Energy پیشگام در صنعت انرژی پایدار، پتروشیمی و فناوری‌های نوین' :
                'INOVA Energy international group, leading in sustainable energy, petrochemicals and innovative technologies';
            metaDesc.setAttribute('content', description);
        }

        // Update language attributes
        document.documentElement.setAttribute('lang', lang === 'fa' ? 'fa-IR' : 'en-US');

        // Update fonts
        const fontLink = document.querySelector('link[href*="fonts.googleapis.com"]');
        if (fontLink) {
            if (lang === 'fa') {
                fontLink.href = 'https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700;800&display=swap';
            } else {
                fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap';
            }
        }

        // Update navigation menu
        updateNavigationMenu(lang);

        // Update dynamic content
        updateDynamicContent(lang);

        // Update form placeholders
        updateFormPlaceholders(lang);

        // Update aria-labels
        updateAriaLabels(lang);
    }

    // Update navigation menu
    function updateNavigationMenu(lang) {
        const navItems = {
            fa: [
                { href: '#hero', text: 'خانه' },
                { href: '#about', text: 'درباره ما' },
                { href: '#rebranding', text: 'ریبرندینگ' },
                { href: '#activities', text: 'فعالیت‌ها' },
                { href: '#services', text: 'خدمات' },
                { href: '#contact', text: 'تماس' }
            ],
            en: [
                { href: '#hero', text: 'Home' },
                { href: '#about', text: 'About' },
                { href: '#rebranding', text: 'Rebranding' },
                { href: '#activities', text: 'Activities' },
                { href: '#services', text: 'Services' },
                { href: '#contact', text: 'Contact' }
            ]
        };

        // Update main navigation
        const navLinks = document.querySelectorAll('.nav-menu a, .mobile-nav-menu a');
        const currentItems = navItems[lang];

        navLinks.forEach((link, index) => {
            if (currentItems[index]) {
                link.textContent = currentItems[index].text;
            }
        });

        // Update language button text
        const langButtons = document.querySelectorAll('.lang-text, .mobile-lang-text');
        langButtons.forEach(button => {
            button.textContent = lang === 'fa' ? 'EN' : 'فا';
        });
    }

    // Update dynamic content
    function updateDynamicContent(lang) {
        const dynamicTexts = {
            fa: {
                'company-name': 'INOVA',
                'company-tagline': 'Energy',
                'cta-button': 'شروع پروژه',
                'lang-switch-text': 'EN'
            },
            en: {
                'company-name': 'INOVA',
                'company-tagline': 'Energy',
                'cta-button': 'Start Project',
                'lang-switch-text': 'فا'
            }
        };

        Object.keys(dynamicTexts[lang]).forEach(className => {
            const elements = document.querySelectorAll(`.${className}`);
            elements.forEach(element => {
                element.textContent = dynamicTexts[lang][className];
            });
        });
    }

    // Update form placeholders
    function updateFormPlaceholders(lang) {
        const placeholders = {
            fa: {
                'firstName': 'نام',
                'lastName': 'نام خانوادگی',
                'email': 'ایمیل',
                'phone': 'تلفن',
                'company': 'شرکت',
                'message': 'پیام خود را اینجا بنویسید...'
            },
            en: {
                'firstName': 'First Name',
                'lastName': 'Last Name',
                'email': 'Email',
                'phone': 'Phone',
                'company': 'Company',
                'message': 'Write your message here...'
            }
        };

        Object.keys(placeholders[lang]).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.placeholder = placeholders[lang][id];
            }
        });
    }

    // Update aria-labels
    function updateAriaLabels(lang) {
        const ariaLabels = {
            fa: {
                'lang-switch': 'تغییر زبان',
                'menu-toggle': 'منو',
                'social-linkedin': 'لینکدین',
                'social-twitter': 'توییتر',
                'social-instagram': 'اینستاگرام'
            },
            en: {
                'lang-switch': 'Change Language',
                'menu-toggle': 'Menu',
                'social-linkedin': 'LinkedIn',
                'social-twitter': 'Twitter',
                'social-instagram': 'Instagram'
            }
        };

        Object.keys(ariaLabels[lang]).forEach(className => {
            const elements = document.querySelectorAll(`[aria-label][class*="${className}"]`);
            elements.forEach(element => {
                element.setAttribute('aria-label', ariaLabels[lang][className]);
            });
        });
    }

    // Initialize language switcher
    $(document).ready(function () {
        // Add loading spinner styles
        if (!document.getElementById('language-switcher-styles')) {
            const styles = document.createElement('style');
            styles.id = 'language-switcher-styles';
            styles.textContent = `
                .loading-spinner {
                    width: 16px;
                    height: 16px;
                    border: 2px solid rgba(255,255,255,0.3);
                    border-top: 2px solid var(--accent);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                
                .lang-switch:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
            `;
            document.head.appendChild(styles);
        }

        // Handle language change animations
        $(document).on('languageChanged', function (event, newLang) {
            // Animate direction change
            $('body').addClass('transitioning');

            setTimeout(() => {
                $('body').removeClass('transitioning');
            }, 300);
        });

        // Update arrows direction based on language
        function updateArrowDirections() {
            const isRTL = document.body.classList.contains('rtl');
            const arrows = document.querySelectorAll('.btn-arrow path');

            arrows.forEach(arrow => {
                if (isRTL) {
                    arrow.setAttribute('d', 'M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z');
                } else {
                    arrow.setAttribute('d', 'M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z');
                }
            });
        }

        // Initialize arrow directions
        updateArrowDirections();

        // Update arrows on language change
        $(document).on('languageChanged', updateArrowDirections);
    });

})(jQuery);
