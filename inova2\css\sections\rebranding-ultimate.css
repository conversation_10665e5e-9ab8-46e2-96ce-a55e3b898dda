/* ===================================
   INOVA ENERGY REBRANDING SECTION - ULTIMATE
   Professional & Organized Design
   =================================== */

/* Section Base */
.rebranding-section-ultimate {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg,
            rgba(0, 31, 63, 1) 0%,
            rgba(0, 15, 30, 0.98) 50%,
            rgba(0, 31, 63, 1) 100%);
    overflow: hidden;
}

/* Background System */
.rebranding-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.gradient-layer-1 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 30%,
            rgba(0, 163, 255, 0.08) 0%,
            transparent 50%);
    animation: gradientFloat1 20s ease-in-out infinite alternate;
}

.gradient-layer-2 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 80% 70%,
            rgba(0, 255, 209, 0.06) 0%,
            transparent 50%);
    animation: gradientFloat2 25s ease-in-out infinite alternate;
}

.bg-geometric-patterns {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.pattern-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 163, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 163, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: patternMove 30s linear infinite;
}

.pattern-dots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 255, 209, 0.2) 2px, transparent 2px);
    background-size: 80px 80px;
    animation: patternDrift 35s linear infinite;
}

.pattern-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
            transparent 48%,
            rgba(0, 163, 255, 0.05) 49%,
            rgba(0, 163, 255, 0.05) 51%,
            transparent 52%);
    background-size: 100px 100px;
    animation: patternSlide 40s linear infinite;
}

.bg-energy-field {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.energy-wave {
    position: absolute;
    width: 200%;
    height: 200%;
    border-radius: 50%;
    border: 1px solid rgba(0, 255, 209, 0.1);
    animation: waveExpand 15s ease-out infinite;
}

.wave-1 {
    top: 10%;
    left: -50%;
    animation-delay: 0s;
}

.wave-2 {
    top: 60%;
    right: -50%;
    animation-delay: -5s;
}

.wave-3 {
    bottom: 10%;
    left: -50%;
    animation-delay: -10s;
}

/* Main Container */
.rebranding-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header Ultimate */
.section-header-ultimate {
    text-align: center;
    margin-bottom: 100px;
}

.header-badge-system {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

.badge-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle,
            rgba(0, 163, 255, 0.2) 0%,
            transparent 70%);
    border-radius: 50px;
    animation: badgeGlow 3s ease-in-out infinite alternate;
}

.badge-content {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.3);
    border-radius: 50px;
    padding: 10px 24px;
    backdrop-filter: blur(15px);
    z-index: 2;
}

.badge-icon {
    font-size: 1.2rem;
    filter: drop-shadow(0 0 5px rgba(0, 255, 209, 0.5));
}

.badge-text {
    font-size: 1rem;
    font-weight: 600;
    color: var(--accent);
    text-shadow: 0 0 10px rgba(0, 255, 209, 0.3);
}

/* Section Title Ultimate */
.section-title-ultimate {
    margin-bottom: 30px;
    line-height: 1.1;
}

.title-line-1,
.title-line-2,
.title-line-3 {
    display: block;
    font-weight: 800;
}

.title-line-1 {
    font-size: 2.5rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 8px;
}

.title-line-2 {
    font-size: 3.5rem;
    margin-bottom: 8px;
}

.title-line-3 {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.8);
}

.gradient-text-ultimate {
    background: linear-gradient(135deg,
            var(--primary) 0%,
            var(--accent) 50%,
            var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite alternate;
}

.section-description-ultimate {
    font-size: 1.3rem;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.85);
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
}

/* Content Grid */
.rebranding-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-bottom: 100px;
    align-items: start;
}

/* Brand Evolution Section */
.brand-evolution-section {
    position: relative;
}

.evolution-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 25px;
    padding: 40px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.evolution-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 163, 255, 0.1),
            transparent);
    transition: left 0.6s ease;
}

.evolution-card:hover::before {
    left: 100%;
}

.evolution-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.15);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.header-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 15px;
    transition: all 0.3s ease;
}

.header-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

.header-icon svg {
    width: 24px;
    height: 24px;
    color: white;
    stroke-width: 2;
}

.card-title {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--text-light);
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Evolution Timeline */
.evolution-timeline {
    position: relative;
    padding-left: 30px;
}

.evolution-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom,
            var(--primary),
            var(--accent),
            var(--primary));
    border-radius: 1px;
}

.timeline-item {
    position: relative;
    margin-bottom: 40px;
    padding-left: 40px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.marker-dot {
    width: 12px;
    height: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    z-index: 2;
    position: relative;
}

.marker-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 0.3) 0%,
            transparent 70%);
    border-radius: 50%;
    animation: markerPulse 2s ease-in-out infinite;
}

.timeline-item.active .marker-dot {
    width: 16px;
    height: 16px;
    box-shadow: 0 0 15px rgba(0, 255, 209, 0.6);
}

.timeline-item.active .marker-glow {
    animation: markerPulseActive 1.5s ease-in-out infinite;
}

.timeline-content {
    position: relative;
    z-index: 2;
}

.timeline-year {
    display: inline-block;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--accent);
    background: rgba(0, 255, 209, 0.1);
    padding: 4px 12px;
    border-radius: 15px;
    margin-bottom: 8px;
    border: 1px solid rgba(0, 255, 209, 0.2);
}

.timeline-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 8px;
    line-height: 1.3;
}

.timeline-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Brand Elements Section */
.brand-elements-section {
    position: relative;
}

.elements-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 25px;
    padding: 40px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.elements-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 255, 209, 0.1),
            transparent);
    transition: left 0.6s ease;
}

.elements-card:hover::before {
    left: 100%;
}

.elements-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 255, 209, 0.3);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 255, 209, 0.15);
}

.elements-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    position: relative;
    z-index: 2;
}

.element-item {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.element-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center,
            rgba(0, 163, 255, 0.05) 0%,
            transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.element-item:hover::before {
    opacity: 1;
}

.element-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 163, 255, 0.2);
    transform: translateY(-5px);
}

.element-visual {
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

/* Logo Showcase */
.logo-showcase {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.showcase-logo {
    width: 50px;
    height: 50px;
    object-fit: contain;
    filter: drop-shadow(0 0 10px rgba(0, 255, 209, 0.3));
    transition: all 0.3s ease;
}

.element-item:hover .showcase-logo {
    transform: scale(1.1);
    filter: drop-shadow(0 0 15px rgba(0, 255, 209, 0.5));
}

.logo-glow-effect {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 0.2) 0%,
            transparent 70%);
    border-radius: 50%;
    animation: logoGlow 3s ease-in-out infinite alternate;
}

/* Color Palette */
.color-palette {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 15px;
}

.color-swatch {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.color-swatch::after {
    content: attr(data-color);
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    white-space: nowrap;
}

.color-swatch:hover::after {
    opacity: 1;
}

.color-swatch:hover {
    transform: scale(1.2);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.primary-color {
    background: var(--primary);
}

.accent-color {
    background: var(--accent);
}

.dark-color {
    background: var(--secondary);
}

.light-color {
    background: var(--text-light);
}

/* Typography Showcase */
.typography-showcase {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.font-sample {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.font-sample:hover {
    background: rgba(0, 163, 255, 0.1);
    border-color: rgba(0, 163, 255, 0.3);
    transform: scale(1.1);
}

.primary-font {
    font-family: 'Inter', sans-serif;
}

.secondary-font {
    font-family: 'Vazirmatn', sans-serif;
}

/* Style Showcase */
.style-showcase {
    margin-bottom: 15px;
}

.style-pattern {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.pattern-element {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 4px;
    animation: patternPulse 2s ease-in-out infinite;
}

.pattern-element:nth-child(2) {
    animation-delay: 0.3s;
}

.pattern-element:nth-child(3) {
    animation-delay: 0.6s;
}

.element-info {
    position: relative;
    z-index: 2;
}

.element-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 8px;
    line-height: 1.3;
}

.element-description {
    font-size: 0.9rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

/* Brand Values Section */
.brand-values-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 30px;
    padding: 60px 40px;
    backdrop-filter: blur(25px);
    position: relative;
    overflow: hidden;
}

.brand-values-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%,
            rgba(0, 163, 255, 0.03) 0%,
            transparent 70%);
    animation: valuesBgPulse 8s ease-in-out infinite alternate;
}

.values-header {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
    z-index: 2;
}

.values-title {
    font-size: 2.2rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    line-height: 1.2;
}

.values-subtitle {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    position: relative;
    z-index: 2;
}

.value-item {
    text-align: center;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.value-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center,
            rgba(0, 255, 209, 0.05) 0%,
            transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.value-item:hover::before {
    opacity: 1;
}

.value-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 255, 209, 0.2);
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 255, 209, 0.1);
}

.value-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 20px;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
}

.value-item:hover .value-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 25px rgba(0, 255, 209, 0.3);
}

.value-icon svg {
    width: 32px;
    height: 32px;
    color: white;
    stroke-width: 2;
}

.value-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 12px;
    line-height: 1.3;
    position: relative;
    z-index: 2;
}

.value-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    position: relative;
    z-index: 2;
}

/* Animations */
@keyframes gradientFloat1 {
    0% {
        opacity: 0.6;
        transform: scale(1) rotate(0deg);
    }

    100% {
        opacity: 1;
        transform: scale(1.05) rotate(2deg);
    }
}

@keyframes gradientFloat2 {
    0% {
        opacity: 0.4;
        transform: scale(1.02) rotate(1deg);
    }

    100% {
        opacity: 0.8;
        transform: scale(1) rotate(-1deg);
    }
}

@keyframes patternMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-50px, -50px);
    }
}

@keyframes patternDrift {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-80px, -80px);
    }
}

@keyframes patternSlide {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-100px, -100px);
    }
}

@keyframes waveExpand {
    0% {
        transform: scale(0);
        opacity: 1;
    }

    100% {
        transform: scale(1);
        opacity: 0;
    }
}

@keyframes badgeGlow {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }

    100% {
        background-position: 100% 50%;
    }
}

@keyframes markerPulse {

    0%,
    100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes markerPulseActive {

    0%,
    100% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.5);
    }
}

@keyframes logoGlow {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes patternPulse {

    0%,
    100% {
        opacity: 0.6;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

@keyframes valuesBgPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.6;
        transform: scale(1.02);
    }
}

/* ===================================
   MOBILE RESPONSIVE DESIGN
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .rebranding-section-ultimate {
        padding: 100px 0;
    }

    .rebranding-container {
        padding: 0 30px;
    }

    .section-header-ultimate {
        margin-bottom: 80px;
    }

    .title-line-1 {
        font-size: 2.2rem;
    }

    .title-line-2 {
        font-size: 3rem;
    }

    .title-line-3 {
        font-size: 1.8rem;
    }

    .section-description-ultimate {
        font-size: 1.2rem;
    }

    .rebranding-content-grid {
        gap: 50px;
        margin-bottom: 80px;
    }

    .evolution-card,
    .elements-card {
        padding: 35px;
    }

    .elements-grid {
        gap: 20px;
    }

    .values-grid {
        gap: 30px;
    }

    .brand-values-section {
        padding: 50px 30px;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .rebranding-section-ultimate {
        padding: 80px 0;
    }

    .rebranding-container {
        padding: 0 20px;
    }

    .section-header-ultimate {
        margin-bottom: 60px;
    }

    .badge-content {
        padding: 8px 20px;
        gap: 8px;
    }

    .badge-icon {
        font-size: 1rem;
    }

    .badge-text {
        font-size: 0.9rem;
    }

    .title-line-1 {
        font-size: 1.8rem;
    }

    .title-line-2 {
        font-size: 2.5rem;
    }

    .title-line-3 {
        font-size: 1.5rem;
    }

    .section-description-ultimate {
        font-size: 1.1rem;
        line-height: 1.7;
    }

    .rebranding-content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
        margin-bottom: 60px;
    }

    .evolution-card,
    .elements-card {
        padding: 25px 20px;
        border-radius: 20px;
    }

    .card-header {
        gap: 12px;
        margin-bottom: 25px;
    }

    .header-icon {
        width: 45px;
        height: 45px;
        border-radius: 12px;
    }

    .header-icon svg {
        width: 22px;
        height: 22px;
    }

    .card-title {
        font-size: 1.4rem;
    }

    .evolution-timeline {
        padding-left: 25px;
    }

    .timeline-item {
        padding-left: 35px;
        margin-bottom: 30px;
    }

    .timeline-marker {
        left: -20px;
    }

    .marker-dot {
        width: 10px;
        height: 10px;
    }

    .timeline-item.active .marker-dot {
        width: 14px;
        height: 14px;
    }

    .timeline-year {
        font-size: 0.8rem;
        padding: 3px 10px;
    }

    .timeline-title {
        font-size: 1.1rem;
    }

    .timeline-description {
        font-size: 0.95rem;
    }

    .elements-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .element-item {
        padding: 20px;
    }

    .logo-showcase {
        width: 50px;
        height: 50px;
    }

    .showcase-logo {
        width: 40px;
        height: 40px;
    }

    .color-swatch {
        width: 22px;
        height: 22px;
    }

    .font-sample {
        width: 35px;
        height: 35px;
        font-size: 1.3rem;
    }

    .pattern-element {
        width: 18px;
        height: 18px;
    }

    .element-title {
        font-size: 1rem;
    }

    .element-description {
        font-size: 0.85rem;
    }

    .brand-values-section {
        padding: 40px 20px;
        border-radius: 25px;
    }

    .values-title {
        font-size: 1.8rem;
    }

    .values-subtitle {
        font-size: 1rem;
    }

    .values-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .value-item {
        padding: 25px 15px;
    }

    .value-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
    }

    .value-icon svg {
        width: 28px;
        height: 28px;
    }

    .value-title {
        font-size: 1.2rem;
    }

    .value-description {
        font-size: 0.95rem;
    }

    /* Slower animations for mobile */
    .gradient-layer-1 {
        animation: gradientFloat1 30s ease-in-out infinite alternate;
    }

    .gradient-layer-2 {
        animation: gradientFloat2 35s ease-in-out infinite alternate;
    }

    .pattern-grid {
        animation: patternMove 45s linear infinite;
    }

    .pattern-dots {
        animation: patternDrift 50s linear infinite;
    }

    .pattern-lines {
        animation: patternSlide 55s linear infinite;
    }

    .energy-wave {
        animation: waveExpand 20s ease-out infinite;
    }

    .badge-glow {
        animation: badgeGlow 5s ease-in-out infinite alternate;
    }

    .gradient-text-ultimate {
        animation: gradientShift 6s ease-in-out infinite alternate;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .rebranding-section-ultimate {
        padding: 60px 0;
    }

    .rebranding-container {
        padding: 0 15px;
    }

    .section-header-ultimate {
        margin-bottom: 50px;
    }

    .title-line-1 {
        font-size: 1.6rem;
    }

    .title-line-2 {
        font-size: 2.2rem;
    }

    .title-line-3 {
        font-size: 1.3rem;
    }

    .section-description-ultimate {
        font-size: 1rem;
        line-height: 1.6;
    }

    .rebranding-content-grid {
        gap: 30px;
        margin-bottom: 50px;
    }

    .evolution-card,
    .elements-card {
        padding: 20px 15px;
    }

    .card-title {
        font-size: 1.3rem;
    }

    .timeline-item {
        margin-bottom: 25px;
    }

    .timeline-title {
        font-size: 1rem;
    }

    .timeline-description {
        font-size: 0.9rem;
    }

    .brand-values-section {
        padding: 30px 15px;
    }

    .values-title {
        font-size: 1.6rem;
    }

    .values-subtitle {
        font-size: 0.95rem;
    }

    .value-item {
        padding: 20px 12px;
    }

    .value-icon {
        width: 50px;
        height: 50px;
    }

    .value-icon svg {
        width: 24px;
        height: 24px;
    }

    .value-title {
        font-size: 1.1rem;
    }

    .value-description {
        font-size: 0.9rem;
    }
}

/* RTL Support */
.rtl .rebranding-content-grid {
    direction: rtl;
}

.rtl .card-header {
    flex-direction: row-reverse;
}

.rtl .evolution-timeline {
    padding-left: 0;
    padding-right: 30px;
}

.rtl .evolution-timeline::before {
    left: auto;
    right: 15px;
}

.rtl .timeline-item {
    padding-left: 0;
    padding-right: 40px;
}

.rtl .timeline-marker {
    left: auto;
    right: -22px;
}

@media (max-width: 767px) {
    .rtl .evolution-timeline {
        padding-right: 25px;
    }

    .rtl .timeline-item {
        padding-right: 35px;
    }

    .rtl .timeline-marker {
        right: -20px;
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .rebranding-section-ultimate *,
    .rebranding-section-ultimate *::before,
    .rebranding-section-ultimate *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .rebranding-section-ultimate {
        background: #000;
    }

    .evolution-card,
    .elements-card,
    .brand-values-section,
    .value-item {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .timeline-description,
    .element-description,
    .value-description,
    .section-description-ultimate,
    .values-subtitle {
        color: #fff;
    }
}