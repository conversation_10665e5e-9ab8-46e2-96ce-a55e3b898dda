/* ===================================
   INOVA ENERGY HEADER - MOBILE ULTIMATE
   Professional Mobile Header Design
   =================================== */

/* Header Base - Mobile Optimized */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(135deg, rgba(0, 31, 63, 0.95) 0%, rgba(0, 15, 30, 0.95) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 255, 209, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;
    padding: 0;
    min-height: 70px;
    box-shadow: 0 2px 20px rgba(0, 255, 209, 0.1);
}

.site-header.scrolled {
    background: linear-gradient(135deg, rgba(0, 31, 63, 0.98) 0%, rgba(0, 15, 30, 0.98) 100%);
    border-bottom-color: rgba(0, 255, 209, 0.3);
    box-shadow: 0 4px 30px rgba(0, 255, 209, 0.15);
}

/* Header Container */
.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    position: relative;
}

/* Site Branding */
.site-branding {
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1001;
}

.site-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: drop-shadow(0 0 8px rgba(0, 255, 209, 0.3));
    transition: all 0.3s ease;
}

.site-logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 12px rgba(0, 255, 209, 0.5));
}

.site-title {
    font-size: 1.4rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    transition: all 0.3s ease;
}

.site-title:hover {
    transform: scale(1.02);
}

/* Main Navigation - Desktop */
.main-navigation {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    padding: 8px 16px;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 163, 255, 0.2),
            transparent);
    transition: left 0.5s ease;
}

.nav-menu a:hover::before {
    left: 100%;
}

.nav-menu a:hover {
    background: rgba(0, 163, 255, 0.1);
    color: var(--accent);
    transform: translateY(-2px);
}

/* Language Switcher */
.language-switcher {
    display: flex;
    align-items: center;
    z-index: 1001;
}

.lang-toggle {
    background: rgba(0, 255, 209, 0.1);
    border: 1px solid rgba(0, 255, 209, 0.3);
    color: var(--text-light);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.lang-toggle:hover {
    background: rgba(0, 163, 255, 0.2);
    border-color: rgba(0, 163, 255, 0.5);
    transform: scale(1.05);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.3);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1002;
}

.mobile-menu-toggle:hover {
    background: rgba(0, 163, 255, 0.2);
    border-color: rgba(0, 163, 255, 0.5);
}

.hamburger-line {
    width: 20px;
    height: 2px;
    background: var(--text-light);
    margin: 2px 0;
    transition: all 0.3s ease;
    border-radius: 1px;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.mobile-navigation {
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    height: calc(100vh - 70px);
    background: linear-gradient(135deg, rgba(0, 31, 63, 0.98) 0%, rgba(0, 15, 30, 0.98) 100%);
    backdrop-filter: blur(25px);
    transform: translateX(-100%);
    transition: transform 0.4s ease;
    z-index: 999;
    overflow-y: auto;
}

.mobile-navigation.active {
    transform: translateX(0);
}

.mobile-nav-menu {
    list-style: none;
    margin: 0;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.mobile-nav-menu li {
    position: relative;
}

.mobile-nav-menu a {
    display: block;
    color: var(--text-light);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.2rem;
    padding: 16px 20px;
    background: rgba(0, 255, 209, 0.05);
    border: 1px solid rgba(0, 255, 209, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mobile-nav-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 163, 255, 0.1),
            transparent);
    transition: left 0.5s ease;
}

.mobile-nav-menu a:hover::before {
    left: 100%;
}

.mobile-nav-menu a:hover {
    background: rgba(0, 163, 255, 0.1);
    border-color: rgba(0, 163, 255, 0.3);
    color: var(--accent);
    transform: translateX(10px);
}

/* Mobile Language Switcher */
.mobile-lang-switcher {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 20px;
}

.mobile-lang-toggle {
    width: 100%;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.3);
    color: var(--accent);
    padding: 16px 20px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.mobile-lang-toggle:hover {
    background: rgba(0, 163, 255, 0.2);
    border-color: rgba(0, 163, 255, 0.5);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .header-container {
        padding: 0 30px;
    }

    .nav-menu {
        gap: 20px;
    }

    .nav-menu a {
        font-size: 0.95rem;
        padding: 6px 12px;
    }

    .site-title {
        font-size: 1.3rem;
    }

    .site-logo {
        width: 38px;
        height: 38px;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .site-header {
        min-height: 65px;
    }

    .header-container {
        height: 65px;
        padding: 0 15px;
    }

    .site-logo {
        width: 35px;
        height: 35px;
    }

    .site-title {
        font-size: 1.2rem;
    }

    .main-navigation {
        display: none;
    }

    .language-switcher {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-navigation {
        top: 65px;
        height: calc(100vh - 65px);
    }

    .mobile-nav-menu {
        padding: 30px 15px;
        gap: 15px;
    }

    .mobile-nav-menu a {
        font-size: 1.1rem;
        padding: 14px 16px;
    }

    .mobile-lang-switcher {
        padding: 15px;
    }

    .mobile-lang-toggle {
        padding: 14px 16px;
        font-size: 1rem;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .site-header {
        min-height: 60px;
    }

    .header-container {
        height: 60px;
        padding: 0 12px;
    }

    .site-branding {
        gap: 8px;
    }

    .site-logo {
        width: 32px;
        height: 32px;
    }

    .site-title {
        font-size: 1.1rem;
    }

    .mobile-menu-toggle {
        width: 36px;
        height: 36px;
    }

    .hamburger-line {
        width: 18px;
    }

    .mobile-navigation {
        top: 60px;
        height: calc(100vh - 60px);
    }

    .mobile-nav-menu {
        padding: 25px 12px;
        gap: 12px;
    }

    .mobile-nav-menu a {
        font-size: 1rem;
        padding: 12px 14px;
    }
}

/* RTL Support */
.rtl .header-container {
    direction: rtl;
}

.rtl .site-branding {
    flex-direction: row-reverse;
}

.rtl .nav-menu {
    direction: rtl;
}

.rtl .mobile-nav-menu a:hover {
    transform: translateX(-10px);
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .site-header *,
    .site-header *::before,
    .site-header *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .site-header {
        background: #000;
        border-bottom-color: var(--primary);
    }

    .nav-menu a,
    .mobile-nav-menu a {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .lang-toggle,
    .mobile-lang-toggle,
    .mobile-menu-toggle {
        border-color: var(--primary);
        background: rgba(0, 163, 255, 0.2);
    }
}