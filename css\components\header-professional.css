/* ===================================
   HEADER - PROFESSIONAL DESIGN
   Ultra-advanced header with cutting-edge effects
   =================================== */

/* Header Base */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background: linear-gradient(135deg, 
        rgba(0, 15, 30, 0.95) 0%, 
        rgba(0, 31, 63, 0.95) 50%,
        rgba(0, 15, 30, 0.95) 100%);
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(0, 255, 209, 0.1);
    transition: all 0.4s ease;
    box-shadow: 0 4px 30px rgba(0, 163, 255, 0.1);
}

/* Header Background Effects */
.site-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(ellipse at 20% 50%, rgba(0, 255, 209, 0.05) 0%, transparent 50%),
        radial-gradient(ellipse at 80% 50%, rgba(0, 163, 255, 0.05) 0%, transparent 50%);
    animation: headerGlow 8s ease-in-out infinite;
    pointer-events: none;
}

.site-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(0, 255, 209, 0.5) 50%, 
        transparent 100%);
    animation: borderPulse 4s ease-in-out infinite;
}

/* Scrolled State */
.site-header.scrolled {
    background: linear-gradient(135deg, 
        rgba(0, 15, 30, 0.98) 0%, 
        rgba(0, 31, 63, 0.98) 50%,
        rgba(0, 15, 30, 0.98) 100%);
    box-shadow: 0 8px 40px rgba(0, 163, 255, 0.2);
    border-bottom-color: rgba(0, 255, 209, 0.2);
}

/* Header Container */
.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    position: relative;
    z-index: 2;
}

/* Logo */
.site-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: all 0.4s ease;
    position: relative;
}

.site-logo::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, 
        rgba(0, 255, 209, 0.1) 0%, 
        transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.site-logo:hover::before {
    opacity: 1;
}

.logo-image {
    height: 50px;
    width: auto;
    transition: all 0.4s ease;
    filter: brightness(1.1) contrast(1.1);
}

.site-logo:hover .logo-image {
    transform: scale(1.05);
    filter: brightness(1.2) contrast(1.2) drop-shadow(0 0 20px rgba(0, 255, 209, 0.3));
}

/* Navigation */
.nav-menu {
    display: flex;
    align-items: center;
    gap: 40px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    position: relative;
    padding: 10px 0;
    transition: all 0.4s ease;
    display: flex;
    align-items: center;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    transition: width 0.4s ease;
    border-radius: 2px;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, 
        rgba(0, 255, 209, 0.1) 0%, 
        transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s ease;
    z-index: -1;
}

.nav-menu a:hover {
    color: var(--accent);
    transform: translateY(-2px);
}

.nav-menu a:hover::before {
    width: 100%;
}

.nav-menu a:hover::after {
    width: 120px;
    height: 120px;
}

.nav-menu a.active {
    color: var(--accent);
}

.nav-menu a.active::before {
    width: 100%;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Language Toggle */
.lang-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.lang-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(0, 255, 209, 0.1) 0%, 
        rgba(0, 163, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.lang-toggle:hover::before {
    opacity: 1;
}

.lang-toggle:hover {
    border-color: rgba(0, 255, 209, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 163, 255, 0.2);
}

.lang-icon {
    width: 18px;
    height: 18px;
    transition: transform 0.4s ease;
}

.lang-toggle:hover .lang-icon {
    transform: rotate(180deg);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 255, 209, 0.3);
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 2px;
    transition: all 0.4s ease;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    height: calc(100vh - 80px);
    background: linear-gradient(135deg, 
        rgba(0, 15, 30, 0.98) 0%, 
        rgba(0, 31, 63, 0.98) 100%);
    backdrop-filter: blur(25px);
    transform: translateX(-100%);
    transition: transform 0.4s ease;
    z-index: 999;
    overflow-y: auto;
}

.mobile-nav.active {
    transform: translateX(0);
}

.mobile-nav-menu {
    display: flex;
    flex-direction: column;
    padding: 40px;
    gap: 30px;
}

.mobile-nav-menu a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.2rem;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
    position: relative;
}

.mobile-nav-menu a::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    transition: width 0.4s ease;
}

.mobile-nav-menu a:hover {
    color: var(--accent);
    padding-left: 20px;
}

.mobile-nav-menu a:hover::before {
    width: 100%;
}

.mobile-lang-toggle {
    margin-top: 20px;
    align-self: flex-start;
}

/* Animations */
@keyframes headerGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

@keyframes borderPulse {
    0%, 100% { opacity: 0.3; transform: scaleX(0.8); }
    50% { opacity: 1; transform: scaleX(1); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .nav-menu {
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 20px;
        height: 70px;
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .mobile-nav {
        top: 70px;
        height: calc(100vh - 70px);
    }
    
    .logo-image {
        height: 40px;
    }
}

@media (max-width: 480px) {
    .header-container {
        padding: 0 15px;
        height: 65px;
    }
    
    .mobile-nav {
        top: 65px;
        height: calc(100vh - 65px);
    }
    
    .mobile-nav-menu {
        padding: 30px 20px;
        gap: 25px;
    }
    
    .logo-image {
        height: 35px;
    }
}
