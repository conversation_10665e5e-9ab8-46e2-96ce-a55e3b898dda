/* Hero Text Styles */
.hero-text {
    position: relative;
    color: #fff;
    text-align: center;
    padding: 100px 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/hero-bg.jpg') no-repeat center center/cover;
    transition: background 0.5s ease-in-out;
}

.hero-text h1 {
    font-size: 3rem;
    margin: 0;
    animation: fadeInUp 1s ease forwards;
}

.hero-text p {
    font-size: 1.5rem;
    margin: 20px 0 0;
    animation: fadeInUp 1.2s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 768px) {
    .hero-text {
        padding: 60px 10px;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-text p {
        font-size: 1.2rem;
    }
}

/* Smooth Scroll Transition */
html {
    scroll-behavior: smooth;
}