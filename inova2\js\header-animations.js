// File: c:\wamp\www\inova2\js\header-animations.js

document.addEventListener('DOMContentLoaded', function() {
    const header = document.querySelector('header');
    const navLinks = document.querySelectorAll('nav a');

    // Add scroll event listener for header animation
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // Add hover effect for navigation links
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', () => {
            link.classList.add('hover');
        });
        link.addEventListener('mouseleave', () => {
            link.classList.remove('hover');
        });
    });
});