/* Activities Section Container */
.activities-section {
    position: relative;
    padding: 6rem 0;
    background: linear-gradient(135deg, #001832 0%, #003366 100%);
    overflow: hidden;
}

/* Background Elements */
.activities-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.gradient-mesh {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(45deg, rgba(0, 163, 255, 0.05) 1px, transparent 1px),
        linear-gradient(-45deg, rgba(0, 255, 209, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: meshFloat 20s linear infinite;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Section Container */
.activities-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title.gradient-text {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
}

/* Activities Grid */
.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

/* Activity Card */
.activity-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

/* Card Header */
.card-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.icon-wrapper {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 163, 255, 0.1);
    border-radius: 50%;
    position: relative;
}

.icon-wrapper::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid rgba(0, 163, 255, 0.2);
    animation: pulseRing 2s infinite;
}

.card-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
}

/* Card Content */
.card-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Project Stats */
.project-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Description */
.description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-badge.active {
    background: rgba(0, 255, 0, 0.1);
    color: #00ff00;
}

.status-badge.in-progress {
    background: rgba(255, 166, 0, 0.1);
    color: #ffa600;
}

.status-badge.planning {
    background: rgba(0, 163, 255, 0.1);
    color: var(--primary);
}

.status-badge .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulseDot 2s infinite;
}

/* Tags */
.product-tags, .service-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    padding: 0.3rem 0.8rem;
    background: rgba(0, 163, 255, 0.1);
    border-radius: 15px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Featured Card */
.activity-card.featured {
    border: 1px solid rgba(0, 163, 255, 0.2);
    background: linear-gradient(135deg, rgba(0, 163, 255, 0.05) 0%, rgba(0, 255, 209, 0.05) 100%);
}

/* Partner Badge */
.partner-badge {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.partner-badge img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

/* Animations */
@keyframes pulseRing {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.5; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes pulseDot {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes meshFloat {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-20px) rotate(1deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .activities-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .activities-section {
        padding: 4rem 0;
    }

    .section-title.gradient-text {
        font-size: 2rem;
    }

    .activities-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .card-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .project-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .activities-container {
        padding: 0 1rem;
    }

    .activity-card {
        padding: 1.5rem;
    }

    .project-stats {
        grid-template-columns: 1fr;
    }

    .status-badge {
        width: 100%;
        justify-content: center;
    }
}
