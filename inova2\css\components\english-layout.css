/* English Layout Specific Styles */

/* English Typography */
.lang-en {
    font-family: 'Inter', 'Roboto', sans-serif;
    letter-spacing: 0.01em;
}

.lang-en h1, .lang-en h2, .lang-en h3, .lang-en h4, .lang-en h5, .lang-en h6 {
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.lang-en h1 {
    font-size: 3.5rem;
    font-weight: 700;
}

.lang-en h2 {
    font-size: 2.5rem;
    font-weight: 600;
}

.lang-en h3 {
    font-size: 1.8rem;
    font-weight: 600;
}

.lang-en h4 {
    font-size: 1.4rem;
    font-weight: 500;
}

.lang-en p, .lang-en span, .lang-en div {
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* English Button Styles */
.lang-en .action-btn,
.lang-en .submit-btn {
    font-weight: 500;
    letter-spacing: 0.02em;
}

.lang-en .cta-button {
    padding: 12px 24px;
}

/* English Navigation */
.lang-en .nav-link {
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* English Form Styles */
.lang-en .form-group label {
    font-weight: 500;
    letter-spacing: 0.01em;
}

.lang-en .form-group input,
.lang-en .form-group textarea,
.lang-en .form-group select {
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.01em;
}

/* English Card Content */
.lang-en .feature-title,
.lang-en .service-title,
.lang-en .project-title {
    font-weight: 600;
    letter-spacing: -0.01em;
}

.lang-en .feature-desc,
.lang-en .service-description,
.lang-en .project-description {
    line-height: 1.6;
    letter-spacing: 0.005em;
}

/* English Statistics */
.lang-en .stat-number {
    font-weight: 700;
    letter-spacing: -0.02em;
}

.lang-en .stat-label {
    font-weight: 500;
    letter-spacing: 0.01em;
    text-transform: uppercase;
    font-size: 0.85em;
}

/* English Timeline */
.lang-en .timeline-title-item {
    font-weight: 600;
    letter-spacing: -0.01em;
}

.lang-en .timeline-desc {
    line-height: 1.6;
    letter-spacing: 0.005em;
}

/* English Section Headers */
.lang-en .section-title {
    font-weight: 700;
    letter-spacing: -0.03em;
}

.lang-en .section-subtitle {
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 1.6;
}

/* English Gradient Text */
.lang-en .gradient-text {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* English Tags and Badges */
.lang-en .tag,
.lang-en .status-badge,
.lang-en .year-badge {
    font-weight: 500;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    font-size: 0.8em;
}

/* English Contact Information */
.lang-en .contact-details h4 {
    font-weight: 600;
    letter-spacing: -0.01em;
}

.lang-en .contact-details p {
    font-weight: 400;
    letter-spacing: 0.01em;
}

/* English Footer */
.lang-en .footer-title {
    font-weight: 600;
    letter-spacing: -0.01em;
    text-transform: uppercase;
    font-size: 0.9em;
}

.lang-en .footer-description {
    line-height: 1.6;
    letter-spacing: 0.005em;
}

/* English Responsive Adjustments */
@media (max-width: 768px) {
    .lang-en h1 {
        font-size: 2.5rem;
    }
    
    .lang-en h2 {
        font-size: 2rem;
    }
    
    .lang-en h3 {
        font-size: 1.5rem;
    }
    
    .lang-en .section-title {
        font-size: 2rem;
        line-height: 1.3;
    }
}

@media (max-width: 480px) {
    .lang-en h1 {
        font-size: 2rem;
    }
    
    .lang-en h2 {
        font-size: 1.8rem;
    }
    
    .lang-en h3 {
        font-size: 1.3rem;
    }
    
    .lang-en .section-title {
        font-size: 1.8rem;
    }
}

/* English Animation Adjustments */
.lang-en .fade-in {
    animation: fadeInEn 0.8s ease-out forwards;
}

@keyframes fadeInEn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* English Hover Effects */
.lang-en .feature-circle:hover .feature-title,
.lang-en .service-card:hover .service-title,
.lang-en .project-card:hover .project-title {
    color: var(--accent);
    transition: color 0.3s ease;
}

/* English Text Selection */
.lang-en ::selection {
    background: rgba(0, 163, 255, 0.2);
    color: var(--text-light);
}

.lang-en ::-moz-selection {
    background: rgba(0, 163, 255, 0.2);
    color: var(--text-light);
}

/* English Scrollbar */
.lang-en ::-webkit-scrollbar {
    width: 8px;
}

.lang-en ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.lang-en ::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 4px;
}

.lang-en ::-webkit-scrollbar-thumb:hover {
    background: var(--accent);
}

/* English Focus States */
.lang-en input:focus,
.lang-en textarea:focus,
.lang-en select:focus,
.lang-en button:focus {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
}

/* English Accessibility */
.lang-en .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* English Print Styles */
@media print {
    .lang-en {
        font-family: 'Times New Roman', serif;
        color: #000;
        background: #fff;
    }
    
    .lang-en .gradient-text {
        -webkit-text-fill-color: initial;
        background: none;
        color: #000;
    }
}

/* English High Contrast Mode */
@media (prefers-contrast: high) {
    .lang-en {
        --primary: #0066cc;
        --accent: #00cc99;
        --text-light: #ffffff;
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.3);
    }
}

/* English Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .lang-en * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
