/* ===================================
   INOVA ENERGY MOBILE MENU - ULTIMATE
   Professional Mobile Menu System
   =================================== */

class MobileMenuUltimate {
    constructor() {
        this.header = document.getElementById('site-header');
        this.mobileToggle = document.getElementById('mobile-menu-toggle');
        this.mobileNav = document.getElementById('mobile-navigation');
        this.body = document.body;
        this.isOpen = false;
        this.scrollPosition = 0;
        
        if (this.mobileToggle && this.mobileNav) {
            this.init();
        }
    }

    init() {
        // Mobile menu toggle event
        this.mobileToggle.addEventListener('click', () => {
            this.toggleMobileMenu();
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isOpen && 
                !this.mobileNav.contains(e.target) && 
                !this.mobileToggle.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Close menu on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeMobileMenu();
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767 && this.isOpen) {
                this.closeMobileMenu();
            }
        });

        // Header scroll effect
        this.initScrollEffect();

        // Smooth scrolling for navigation links
        this.initSmoothScrolling();
    }

    toggleMobileMenu() {
        if (this.isOpen) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }

    openMobileMenu() {
        // Store current scroll position
        this.scrollPosition = window.pageYOffset;
        
        // Add classes
        this.mobileToggle.classList.add('active');
        this.mobileNav.classList.add('active');
        this.body.classList.add('mobile-menu-open');
        
        // Prevent body scroll
        this.body.style.position = 'fixed';
        this.body.style.top = `-${this.scrollPosition}px`;
        this.body.style.width = '100%';
        
        this.isOpen = true;
        
        // Focus management
        this.mobileNav.setAttribute('aria-hidden', 'false');
        
        // Animate menu items
        this.animateMenuItems('in');
    }

    closeMobileMenu() {
        // Remove classes
        this.mobileToggle.classList.remove('active');
        this.mobileNav.classList.remove('active');
        this.body.classList.remove('mobile-menu-open');
        
        // Restore body scroll
        this.body.style.position = '';
        this.body.style.top = '';
        this.body.style.width = '';
        
        // Restore scroll position
        window.scrollTo(0, this.scrollPosition);
        
        this.isOpen = false;
        
        // Focus management
        this.mobileNav.setAttribute('aria-hidden', 'true');
        
        // Animate menu items
        this.animateMenuItems('out');
    }

    animateMenuItems(direction) {
        const menuItems = this.mobileNav.querySelectorAll('.mobile-nav-menu li');
        
        menuItems.forEach((item, index) => {
            if (direction === 'in') {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.3s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 50);
            } else {
                item.style.transition = 'all 0.2s ease';
                item.style.opacity = '0';
                item.style.transform = 'translateY(-10px)';
            }
        });
    }

    initScrollEffect() {
        let lastScrollY = window.pageYOffset;
        let ticking = false;

        const updateHeader = () => {
            const scrollY = window.pageYOffset;
            
            if (scrollY > 50) {
                this.header.classList.add('scrolled');
            } else {
                this.header.classList.remove('scrolled');
            }
            
            // Hide/show header on scroll
            if (scrollY > lastScrollY && scrollY > 100) {
                this.header.style.transform = 'translateY(-100%)';
            } else {
                this.header.style.transform = 'translateY(0)';
            }
            
            lastScrollY = scrollY;
            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateHeader);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick, { passive: true });
    }

    initSmoothScrolling() {
        const navLinks = document.querySelectorAll('a[href^="#"]');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                
                if (href === '#') return;
                
                const target = document.querySelector(href);
                
                if (target) {
                    e.preventDefault();
                    
                    const headerHeight = this.header.offsetHeight;
                    const targetPosition = target.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (this.isOpen) {
                        this.closeMobileMenu();
                    }
                }
            });
        });
    }
}

// Language Toggle Enhancement
function toggleLanguage() {
    const body = document.body;
    const langText = document.querySelector('.lang-text');
    const mobileLangText = document.querySelector('.mobile-lang-text');
    const html = document.documentElement;
    
    if (body.classList.contains('lang-fa')) {
        // Switch to English
        body.classList.remove('lang-fa', 'rtl');
        body.classList.add('lang-en', 'ltr');
        html.setAttribute('lang', 'en');
        html.setAttribute('dir', 'ltr');
        
        if (langText) langText.textContent = 'فا';
        if (mobileLangText) mobileLangText.textContent = 'فارسی';
        
        // Store preference
        localStorage.setItem('inova_language', 'en');
    } else {
        // Switch to Persian
        body.classList.remove('lang-en', 'ltr');
        body.classList.add('lang-fa', 'rtl');
        html.setAttribute('lang', 'fa');
        html.setAttribute('dir', 'rtl');
        
        if (langText) langText.textContent = 'EN';
        if (mobileLangText) mobileLangText.textContent = 'English';
        
        // Store preference
        localStorage.setItem('inova_language', 'fa');
    }
    
    // Trigger custom event
    const event = new CustomEvent('languageChanged', {
        detail: { 
            language: body.classList.contains('lang-fa') ? 'fa' : 'en' 
        }
    });
    document.dispatchEvent(event);
}

// Close Mobile Menu Function (for onclick handlers)
function closeMobileMenu() {
    if (window.mobileMenuInstance) {
        window.mobileMenuInstance.closeMobileMenu();
    }
}

// Initialize when DOM is ready
function initMobileMenu() {
    // Load saved language preference
    const savedLang = localStorage.getItem('inova_language');
    if (savedLang) {
        const body = document.body;
        const html = document.documentElement;
        const langText = document.querySelector('.lang-text');
        const mobileLangText = document.querySelector('.mobile-lang-text');
        
        if (savedLang === 'en') {
            body.classList.remove('lang-fa', 'rtl');
            body.classList.add('lang-en', 'ltr');
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            if (langText) langText.textContent = 'فا';
            if (mobileLangText) mobileLangText.textContent = 'فارسی';
        } else {
            body.classList.remove('lang-en', 'ltr');
            body.classList.add('lang-fa', 'rtl');
            html.setAttribute('lang', 'fa');
            html.setAttribute('dir', 'rtl');
            if (langText) langText.textContent = 'EN';
            if (mobileLangText) mobileLangText.textContent = 'English';
        }
    }
    
    // Initialize mobile menu
    window.mobileMenuInstance = new MobileMenuUltimate();
}

// Initialize
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMobileMenu);
} else {
    initMobileMenu();
}

// Performance optimization for mobile
if ('ontouchstart' in window) {
    // Add touch-specific optimizations
    document.body.classList.add('touch-device');
    
    // Prevent 300ms click delay on mobile
    let touchStartTime = 0;
    
    document.addEventListener('touchstart', (e) => {
        touchStartTime = Date.now();
    }, { passive: true });
    
    document.addEventListener('touchend', (e) => {
        const touchEndTime = Date.now();
        const touchDuration = touchEndTime - touchStartTime;
        
        // If touch was quick (like a tap), add fast-tap class
        if (touchDuration < 150) {
            e.target.classList.add('fast-tap');
            setTimeout(() => {
                e.target.classList.remove('fast-tap');
            }, 150);
        }
    }, { passive: true });
}

// Export for global access
window.MobileMenuUltimate = MobileMenuUltimate;
window.toggleLanguage = toggleLanguage;
window.closeMobileMenu = closeMobileMenu;
