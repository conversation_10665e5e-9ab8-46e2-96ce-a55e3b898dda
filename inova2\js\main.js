// File: c:\wamp\www\inova2\js\main.js

document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling to sections
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            targetSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        });
    });

    // Section transition effects
    const sections = document.querySelectorAll('section');
    const options = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    }, options);

    sections.forEach(section => {
        observer.observe(section);
    });
});

// Initialize any additional scripts or libraries if needed
// Example: Initialize particles.js or any other library
// particlesJS.load('particles-js', 'path/to/particles.json', function() {
//     console.log('callback - particles.js config loaded');
// });