/* ===================================
   INOVA ENERGY FOOTER STYLES
   Professional & Responsive Design
   =================================== */

/* Footer Base Styles */
.main-footer {
    position: relative;
    background: linear-gradient(135deg, 
        rgba(0, 31, 63, 0.95) 0%, 
        rgba(0, 31, 63, 0.98) 50%, 
        rgba(0, 15, 30, 1) 100%
    );
    color: var(--text-light);
    padding: 80px 0 0;
    margin-top: 100px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 163, 255, 0.2);
}

/* Footer Background Effects */
.main-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(0, 163, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 255, 209, 0.08) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(0, 163, 255, 0.03) 50%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.main-footer::after {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--primary) 25%, 
        var(--accent) 50%, 
        var(--primary) 75%, 
        transparent 100%
    );
    animation: footerGlow 3s ease-in-out infinite alternate;
}

@keyframes footerGlow {
    0% { opacity: 0.5; transform: scaleX(0.8); }
    100% { opacity: 1; transform: scaleX(1); }
}

/* Footer Container */
.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

/* Footer Content Grid */
.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 60px;
    margin-bottom: 60px;
    align-items: start;
}

/* Footer Logo Section */
.footer-logo {
    position: relative;
}

.footer-logo-link {
    display: flex;
    align-items: center;
    gap: 16px;
    text-decoration: none;
    color: var(--text-light);
    margin-bottom: 24px;
    transition: all 0.3s ease;
}

.footer-logo-link:hover {
    transform: translateY(-2px);
}

.footer-logo .logo-mark {
    width: 50px;
    height: 50px;
    position: relative;
}

.footer-logo .logo-mark svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 10px rgba(0, 163, 255, 0.3));
    transition: all 0.3s ease;
}

.footer-logo-link:hover .logo-mark svg {
    filter: drop-shadow(0 0 20px rgba(0, 255, 209, 0.5));
    transform: rotate(5deg);
}

.footer-logo .logo-type {
    display: flex;
    flex-direction: column;
}

.footer-logo .logo-text {
    font-size: 28px;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 4px;
}

.footer-logo .logo-tagline {
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 2px;
    text-transform: uppercase;
}

/* Footer Description */
.footer-description {
    font-size: 16px;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 32px;
    max-width: 400px;
}

/* Footer Sections */
.footer-nav,
.footer-contact,
.footer-social {
    position: relative;
}

.footer-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 24px;
    position: relative;
    padding-bottom: 12px;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 1px;
}

/* Footer Menu */
.footer-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-menu li {
    margin-bottom: 12px;
}

.footer-menu a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 15px;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    padding: 4px 0;
}

.footer-menu a::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 1px;
    background: var(--accent);
    transition: width 0.3s ease;
}

.footer-menu a:hover {
    color: var(--accent);
    transform: translateX(8px);
}

.footer-menu a:hover::before {
    width: 100%;
}

/* Contact Items */
.contact-items {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 0;
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(8px);
}

.contact-icon {
    width: 20px;
    height: 20px;
    color: var(--primary);
    flex-shrink: 0;
}

.contact-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.contact-item span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 15px;
    font-weight: 400;
}

/* Social Links */
.social-links {
    display: flex;
    gap: 16px;
    margin-top: 24px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 163, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.social-link:hover::before {
    left: 100%;
}

.social-link:hover {
    background: rgba(0, 163, 255, 0.1);
    border-color: var(--primary);
    color: var(--accent);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 163, 255, 0.3);
}

.social-link svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
    transition: all 0.3s ease;
}

.social-link:hover svg {
    transform: scale(1.1);
}

/* Footer Bottom */
.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 40px;
}

.footer-copyright p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    font-weight: 400;
    margin: 0;
}

.footer-links {
    display: flex;
    gap: 32px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.footer-links a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--accent);
    transition: width 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent);
}

.footer-links a:hover::after {
    width: 100%;
}

/* Responsive Design - Tablet */
@media (max-width: 1024px) {
    .main-footer {
        padding: 60px 0 0;
        margin-top: 80px;
    }
    
    .footer-container {
        padding: 0 30px;
    }
    
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        margin-bottom: 40px;
    }
    
    .footer-logo {
        grid-column: 1 / -1;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .footer-description {
        text-align: center;
        margin: 0 auto 24px;
    }
    
    .footer-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .footer-nav,
    .footer-contact,
    .footer-social {
        text-align: center;
    }
    
    .contact-items {
        align-items: center;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Responsive Design - Mobile */
@media (max-width: 768px) {
    .main-footer {
        padding: 50px 0 0;
        margin-top: 60px;
    }
    
    .footer-container {
        padding: 0 20px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .footer-logo-link {
        justify-content: center;
        margin-bottom: 20px;
    }
    
    .footer-logo .logo-text {
        font-size: 24px;
    }
    
    .footer-description {
        font-size: 15px;
        max-width: 100%;
    }
    
    .footer-title {
        font-size: 16px;
        margin-bottom: 20px;
    }
    
    .footer-title::after {
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
    }
    
    .footer-menu a,
    .contact-item span {
        font-size: 14px;
    }
    
    .contact-item {
        justify-content: center;
        padding: 8px 0;
    }
    
    .social-links {
        gap: 12px;
        margin-top: 20px;
    }
    
    .social-link {
        width: 40px;
        height: 40px;
    }
    
    .social-link svg {
        width: 18px;
        height: 18px;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 20px;
        padding: 24px 0;
        text-align: center;
    }
    
    .footer-links {
        gap: 20px;
    }
    
    .footer-copyright p,
    .footer-links a {
        font-size: 13px;
    }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
    .main-footer {
        padding: 40px 0 0;
        margin-top: 50px;
    }
    
    .footer-container {
        padding: 0 15px;
    }
    
    .footer-content {
        gap: 30px;
    }
    
    .footer-logo .logo-text {
        font-size: 22px;
    }
    
    .footer-logo .logo-tagline {
        font-size: 12px;
    }
    
    .footer-description {
        font-size: 14px;
        line-height: 1.6;
    }
    
    .footer-title {
        font-size: 15px;
        margin-bottom: 16px;
    }
    
    .footer-menu a,
    .contact-item span {
        font-size: 13px;
    }
    
    .social-links {
        gap: 10px;
    }
    
    .social-link {
        width: 36px;
        height: 36px;
        border-radius: 10px;
    }
    
    .social-link svg {
        width: 16px;
        height: 16px;
    }
    
    .footer-bottom {
        padding: 20px 0;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 12px;
    }
}

/* RTL Support */
.rtl .footer-content {
    direction: rtl;
}

.rtl .footer-logo-link {
    flex-direction: row-reverse;
}

.rtl .footer-title::after {
    left: auto;
    right: 0;
}

.rtl .footer-menu a:hover {
    transform: translateX(-8px);
}

.rtl .contact-item:hover {
    transform: translateX(-8px);
}

.rtl .contact-item {
    flex-direction: row-reverse;
}

.rtl .footer-bottom {
    flex-direction: row-reverse;
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    .main-footer {
        background: linear-gradient(135deg, 
            rgba(0, 15, 30, 0.98) 0%, 
            rgba(0, 31, 63, 0.95) 50%, 
            rgba(0, 15, 30, 1) 100%
        );
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .main-footer {
        background: #000;
        border-top: 2px solid var(--primary);
    }
    
    .footer-description,
    .footer-menu a,
    .contact-item span {
        color: #fff;
    }
    
    .social-link {
        background: #333;
        border-color: var(--primary);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .main-footer::after,
    .footer-logo-link,
    .footer-menu a,
    .contact-item,
    .social-link {
        animation: none;
        transition: none;
    }
}
