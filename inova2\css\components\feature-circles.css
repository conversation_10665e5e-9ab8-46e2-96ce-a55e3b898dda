/* Feature Circles Styles */

.feature-circle {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 20px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-circle:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.feature-circle-icon {
    font-size: 2rem;
    color: #333;
}

.feature-circle-title {
    margin-top: 10px;
    font-weight: bold;
    text-align: center;
    color: #333;
}

.feature-circle-description {
    font-size: 0.9rem;
    text-align: center;
    color: #666;
}

/* Smooth Section Transitions */
section {
    position: relative;
    overflow: hidden;
    padding: 60px 20px;
    transition: background-color 0.5s ease;
}

section:nth-child(even) {
    background-color: #f9f9f9;
}

section:nth-child(odd) {
    background-color: #ffffff;
}

/* Scroll Animation */
[data-aos] {
    opacity: 0;
    transition-property: opacity, transform;
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

[data-aos="fade-up"] {
    transform: translateY(20px);
}

[data-aos="fade-up"].aos-animate {
    transform: translateY(0);
}

/* Media Queries for Responsiveness */
@media (max-width: 768px) {
    .feature-circle {
        width: 120px;
        height: 120px;
    }

    .feature-circle-title {
        font-size: 1rem;
    }

    .feature-circle-description {
        font-size: 0.8rem;
    }
}