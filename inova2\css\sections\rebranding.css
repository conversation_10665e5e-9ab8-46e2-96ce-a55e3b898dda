/* Rebranding Section Styles */

.rebranding-section {
    background-color: #f9f9f9; /* Light background for contrast */
    padding: 60px 20px; /* Adequate padding for spacing */
    text-align: center; /* Centered text for a clean look */
    position: relative; /* For absolute positioning of elements */
    overflow: hidden; /* Prevent overflow of child elements */
}

.rebranding-section h2 {
    font-size: 2.5rem; /* Large heading for emphasis */
    color: #333; /* Dark text color for readability */
    margin-bottom: 20px; /* Space below the heading */
    transition: transform 0.5s ease; /* Smooth transform effect */
}

.rebranding-section p {
    font-size: 1.2rem; /* Readable paragraph size */
    color: #666; /* Slightly lighter text color */
    margin-bottom: 40px; /* Space below the paragraph */
    transition: opacity 0.5s ease; /* Smooth opacity transition */
}

.rebranding-section .rebranding-content {
    display: flex; /* Flexbox for layout */
    justify-content: center; /* Center content horizontally */
    align-items: center; /* Center content vertically */
    flex-wrap: wrap; /* Allow wrapping of items */
}

.rebranding-section .rebranding-item {
    background: #fff; /* White background for items */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
    margin: 10px; /* Space between items */
    padding: 20px; /* Padding inside items */
    flex: 1 1 300px; /* Responsive item size */
    transition: transform 0.3s ease, box-shadow 0.3s ease; /* Smooth hover effects */
}

.rebranding-section .rebranding-item:hover {
    transform: translateY(-5px); /* Lift effect on hover */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2); /* Enhanced shadow on hover */
}

.rebranding-section .rebranding-image {
    max-width: 100%; /* Responsive image */
    height: auto; /* Maintain aspect ratio */
    border-radius: 8px; /* Rounded corners for images */
}

.rebranding-section .rebranding-button {
    background-color: #007bff; /* Primary button color */
    color: #fff; /* White text for contrast */
    padding: 10px 20px; /* Adequate padding */
    border: none; /* No border */
    border-radius: 5px; /* Rounded corners */
    cursor: pointer; /* Pointer cursor on hover */
    transition: background-color 0.3s ease; /* Smooth background color transition */
}

.rebranding-section .rebranding-button:hover {
    background-color: #0056b3; /* Darker shade on hover */
}

/* Smooth Scroll Transition */
html {
    scroll-behavior: smooth; /* Enable smooth scrolling */
}

/* AOS (Animate On Scroll) Integration */
[data-aos] {
    opacity: 0; /* Start hidden */
    transition: opacity 0.5s ease; /* Smooth opacity transition */
}

[data-aos].aos-animate {
    opacity: 1; /* Fade in when animated */
}