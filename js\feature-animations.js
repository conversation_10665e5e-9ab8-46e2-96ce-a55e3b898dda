document.addEventListener('DOMContentLoaded', () => {
    const features = document.querySelectorAll('.feature-circle');
    
    // Mouse movement parallax effect
    features.forEach(feature => {
        feature.addEventListener('mousemove', (e) => {
            const rect = feature.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const deltaX = (x - centerX) / centerX;
            const deltaY = (y - centerY) / centerY;
            
            feature.style.transform = `
                perspective(1000px)
                rotateX(${deltaY * 5}deg)
                rotateY(${deltaX * 5}deg)
                translateZ(10px)
            `;
            
            const icon = feature.querySelector('.feature-icon');
            icon.style.transform = `
                translate(${deltaX * 10}px, ${deltaY * 10}px)
                rotate(${(deltaX + deltaY) * 5}deg)
            `;
        });
        
        // Reset transform on mouse leave
        feature.addEventListener('mouseleave', () => {
            feature.style.transform = '';
            const icon = feature.querySelector('.feature-icon');
            icon.style.transform = '';
        });
    });
    
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.2,
        rootMargin: '0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    features.forEach(feature => {
        observer.observe(feature);
    });
});

// Add smooth scroll behavior
document.querySelectorAll('.feature-circle').forEach(feature => {
    feature.addEventListener('click', () => {
        const section = feature.closest('section');
        const nextSection = section.nextElementSibling;
        if (nextSection) {
            nextSection.scrollIntoView({ behavior: 'smooth' });
        }
    });
});