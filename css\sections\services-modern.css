/* ===================================
   INOVA ENERGY SERVICES SECTION
   Professional & Interactive Design
   =================================== */

/* Services Section Container */
.services-section {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 0.98) 0%,
            rgba(0, 31, 63, 0.95) 50%,
            rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* Background Effects */
.services-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.gradient-mesh {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 20%, rgba(0, 163, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(0, 255, 209, 0.08) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(0, 163, 255, 0.04) 50%, transparent 70%);
    animation: meshFloat 12s ease-in-out infinite alternate;
}

@keyframes meshFloat {
    0% {
        opacity: 0.7;
        transform: scale(1) rotate(0deg);
    }

    100% {
        opacity: 1;
        transform: scale(1.03) rotate(2deg);
    }
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 163, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0, 255, 209, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 163, 255, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(0, 255, 209, 0.3), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: particleMove 20s linear infinite;
}

@keyframes particleMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-150px, -100px);
    }
}

/* Services Container */
.services-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 24px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 2px;
    animation: titlePulse 3s ease-in-out infinite alternate;
}

@keyframes titlePulse {
    0% {
        width: 80px;
        opacity: 0.7;
    }

    100% {
        width: 100px;
        opacity: 1;
    }
}

.section-subtitle {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 40px;
    margin-bottom: 80px;
}

/* Service Card */
.service-card {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    padding: 40px 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    cursor: pointer;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 163, 255, 0.1),
            transparent);
    transition: left 0.8s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 30px 60px rgba(0, 163, 255, 0.2);
    border-color: rgba(0, 163, 255, 0.3);
}

/* Service Header */
.service-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.2),
            rgba(0, 255, 209, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover .service-icon::before {
    opacity: 1;
}

.service-card:hover .service-icon {
    background: rgba(0, 163, 255, 0.2);
    border-color: rgba(0, 163, 255, 0.4);
    transform: scale(1.1) rotate(5deg);
}

.service-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-light);
    line-height: 1.3;
    flex: 1;
}

/* Service Content */
.service-content {
    position: relative;
    z-index: 2;
}

.service-description {
    font-size: 1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 24px;
}

/* Service Features */
.service-features {
    list-style: none;
    padding: 0;
    margin: 0 0 24px 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.75);
    padding: 8px 0;
    position: relative;
}

.service-features li::before {
    content: '✓';
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    color: white;
    flex-shrink: 0;
}

.service-features li:hover {
    color: rgba(255, 255, 255, 0.9);
    transform: translateX(8px);
}

/* Service Process */
.service-process {
    margin-top: 60px;
    text-align: center;
}

.process-title {
    font-size: 2.2rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 50px;
    position: relative;
    display: inline-block;
}

.process-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 1px;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.process-step {
    position: relative;
    text-align: center;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.process-step:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 163, 255, 0.2);
    transform: translateY(-8px);
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin: 0 auto 20px;
    position: relative;
    z-index: 2;
}

.step-number::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 50%;
    opacity: 0.3;
    z-index: -1;
    animation: numberGlow 2s ease-in-out infinite alternate;
}

@keyframes numberGlow {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

.step-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 12px;
    line-height: 1.3;
}

.step-content p {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.75);
    line-height: 1.6;
}

/* Responsive Design - Large Desktop */
@media (min-width: 1400px) {
    .services-section {
        padding: 120px 0;
    }

    .section-title {
        font-size: 3.5rem;
    }

    .section-subtitle {
        font-size: 1.3rem;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
        gap: 50px;
        margin-bottom: 100px;
    }

    .service-card {
        padding: 50px 40px;
    }

    .service-title {
        font-size: 1.5rem;
    }

    .service-description {
        font-size: 1.1rem;
    }

    .process-title {
        font-size: 2.5rem;
    }

    .process-steps {
        gap: 50px;
    }

    .process-step {
        padding: 40px 30px;
    }
}

/* Responsive Design - Tablet */
@media (max-width: 1024px) {
    .services-section {
        padding: 80px 0;
    }

    .services-container {
        padding: 0 30px;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 30px;
        margin-bottom: 60px;
    }

    .service-card {
        padding: 35px 25px;
        border-radius: 20px;
    }

    .service-header {
        gap: 16px;
        margin-bottom: 20px;
    }

    .service-icon {
        width: 50px;
        height: 50px;
        border-radius: 14px;
    }

    .service-title {
        font-size: 1.3rem;
    }

    .service-description {
        font-size: 0.95rem;
        margin-bottom: 20px;
    }

    .service-features {
        gap: 10px;
        margin-bottom: 20px;
    }

    .service-features li {
        font-size: 0.9rem;
        padding: 6px 0;
    }

    .service-features li::before {
        width: 18px;
        height: 18px;
        font-size: 11px;
    }

    .service-process {
        margin-top: 50px;
    }

    .process-title {
        font-size: 2rem;
        margin-bottom: 40px;
    }

    .process-steps {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 30px;
    }

    .process-step {
        padding: 25px 15px;
        border-radius: 16px;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 16px;
    }

    .step-content h4 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .step-content p {
        font-size: 0.9rem;
    }
}

/* Responsive Design - Mobile */
@media (max-width: 768px) {
    .services-section {
        padding: 60px 0;
    }

    .services-container {
        padding: 0 20px;
    }

    .section-header {
        margin-bottom: 50px;
    }

    .section-title {
        font-size: 2.2rem;
        margin-bottom: 20px;
    }

    .section-title::after {
        width: 60px;
        bottom: -8px;
    }

    .section-subtitle {
        font-size: 1rem;
        line-height: 1.6;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-bottom: 50px;
    }

    .service-card {
        padding: 30px 20px;
        border-radius: 18px;
    }

    .service-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
        margin-bottom: 20px;
    }

    .service-icon {
        width: 50px;
        height: 50px;
        margin: 0 auto;
    }

    .service-title {
        font-size: 1.2rem;
        text-align: center;
    }

    .service-description {
        font-size: 0.9rem;
        text-align: center;
        margin-bottom: 20px;
    }

    .service-features {
        gap: 8px;
        margin-bottom: 16px;
    }

    .service-features li {
        font-size: 0.85rem;
        padding: 5px 0;
        justify-content: center;
    }

    .service-features li:hover {
        transform: translateY(-2px);
    }

    .service-features li::before {
        width: 16px;
        height: 16px;
        font-size: 10px;
    }

    .service-process {
        margin-top: 40px;
    }

    .process-title {
        font-size: 1.8rem;
        margin-bottom: 30px;
    }

    .process-title::after {
        width: 50px;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .process-step {
        padding: 25px 20px;
        border-radius: 16px;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 16px;
    }

    .step-content h4 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .step-content p {
        font-size: 0.85rem;
        line-height: 1.5;
    }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
    .services-section {
        padding: 50px 0;
    }

    .services-container {
        padding: 0 15px;
    }

    .section-header {
        margin-bottom: 40px;
    }

    .section-title {
        font-size: 1.9rem;
        line-height: 1.3;
    }

    .section-subtitle {
        font-size: 0.95rem;
    }

    .services-grid {
        gap: 20px;
        margin-bottom: 40px;
    }

    .service-card {
        padding: 25px 18px;
        border-radius: 16px;
    }

    .service-header {
        gap: 12px;
        margin-bottom: 16px;
    }

    .service-icon {
        width: 45px;
        height: 45px;
        border-radius: 12px;
    }

    .service-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }

    .service-description {
        font-size: 0.85rem;
        margin-bottom: 16px;
    }

    .service-features {
        gap: 6px;
        margin-bottom: 14px;
    }

    .service-features li {
        font-size: 0.8rem;
        padding: 4px 0;
    }

    .service-features li::before {
        width: 14px;
        height: 14px;
        font-size: 9px;
    }

    .process-title {
        font-size: 1.6rem;
        margin-bottom: 25px;
    }

    .process-steps {
        gap: 16px;
    }

    .process-step {
        padding: 20px 16px;
    }

    .step-number {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
        margin-bottom: 12px;
    }

    .step-content h4 {
        font-size: 0.95rem;
        margin-bottom: 6px;
    }

    .step-content p {
        font-size: 0.8rem;
    }
}

/* RTL Support */
.rtl .service-header {
    flex-direction: row-reverse;
}

.rtl .service-features li:hover {
    transform: translateX(-8px);
}

.rtl .section-title::after,
.rtl .process-title::after {
    left: 50%;
    transform: translateX(-50%);
}

@media (max-width: 768px) {
    .rtl .service-header {
        flex-direction: column;
    }

    .rtl .service-features li:hover {
        transform: translateY(-2px);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .services-section {
        background: #000;
    }

    .service-card,
    .process-step {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--primary);
    }

    .service-description,
    .section-subtitle,
    .service-features li,
    .step-content p {
        color: #fff;
    }

    .service-icon {
        background: rgba(0, 163, 255, 0.3);
        border-color: var(--primary);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {

    .services-section *,
    .services-section *::before,
    .services-section *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}