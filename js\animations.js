class Animations {
    constructor() {
        this.initParticles();
        this.initScrollAnimations();
        this.initStatCounters();
        this.initGlobe();
    }

    initParticles() {
        const canvas = document.getElementById('hero-particles');
        if (!canvas) {
            console.warn('Particles canvas not found');
            return;
        }
        
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.warn('Could not get 2D context');
            return;
        }
        
        // Set canvas size
        const setCanvasSize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };
        
        setCanvasSize();
        window.addEventListener('resize', setCanvasSize);

        // Particle system
        const particles = [];
        const particleCount = 100;

        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                radius: Math.random() * 2 + 1,
                vx: Math.random() * 2 - 1,
                vy: Math.random() * 2 - 1,
                alpha: Math.random()
            });
        }

        function animate() {
            requestAnimationFrame(animate);
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.alpha += Math.random() * 0.1 - 0.05;

                if (particle.alpha <= 0) particle.alpha = 0;
                if (particle.alpha >= 1) particle.alpha = 1;

                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(0, 163, 255, ${particle.alpha})`;
                ctx.fill();

                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });
        }

        animate();
    }

    initScrollAnimations() {
        gsap.registerPlugin(ScrollTrigger);

        // Navbar animation
        gsap.to('.floating-nav', {
            scrollTrigger: {
                start: 'top top',
                end: '+=100',
                toggleActions: 'play none none reverse'
            },
            y: -100,
            duration: 0.3
        });

        // Stats counter animation
        gsap.from('.stat-card', {
            scrollTrigger: {
                trigger: '.stats-container',
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            },
            opacity: 0,
            y: 50,
            duration: 1
        });
    }

    initStatCounters() {
        const counters = document.querySelectorAll('.stat-counter');
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'), 10);
            let current = 0;
            const increment = target / 100;

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };

            updateCounter();
        });
    }

    initGlobe() {
        const globe = document.getElementById('globe');
        const globeImage = new Image();
        globeImage.src = './assets/images/globe.png';

        globeImage.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = globe.clientWidth;
            canvas.height = globe.clientHeight;

            const drawGlobe = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(globeImage, 0, 0, canvas.width, canvas.height);
                requestAnimationFrame(drawGlobe);
            };

            drawGlobe();
        };
    }
}

// Initialize animations only when DOM is ready
document.addEventListener('DOMContentLoaded', () => new Animations());








