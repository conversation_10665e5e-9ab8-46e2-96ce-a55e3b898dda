/* ===================================
   INOVA ENERGY HERO SECTION
   Ultra Modern & Professional Design
   =================================== */

/* Hero Section Container */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg,
            rgba(0, 31, 63, 1) 0%,
            rgba(0, 15, 30, 0.95) 50%,
            rgba(0, 31, 63, 0.98) 100%);
    padding: 120px 0 80px;
}

/* Advanced Background Effects */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(0, 163, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 255, 209, 0.12) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(0, 163, 255, 0.05) 50%, transparent 70%);
    animation: backgroundPulse 8s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% {
        opacity: 0.7;
        transform: scale(1);
    }

    100% {
        opacity: 1;
        transform: scale(1.05);
    }
}

/* Particle Canvas */
#energyParticles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    opacity: 0.6;
}

/* Geometric Shapes */
.geometric-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.1),
            rgba(0, 255, 209, 0.08));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: floatShape 12s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: -4s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    top: 80%;
    left: 20%;
    animation-delay: -8s;
}

@keyframes floatShape {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.3;
    }

    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 0.6;
    }

    50% {
        transform: translateY(-40px) rotate(180deg);
        opacity: 0.4;
    }

    75% {
        transform: translateY(-20px) rotate(270deg);
        opacity: 0.7;
    }
}

/* Hero Content Container */
.hero-content {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Grid Layout */
.hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    min-height: 70vh;
}

/* Hero Text Column */
.hero-text-column {
    position: relative;
    z-index: 5;
}

.hero-content-wrapper {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    padding: 50px 40px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.hero-content-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 163, 255, 0.1),
            transparent);
    transition: left 0.8s ease;
}

.hero-content-wrapper:hover::before {
    left: 100%;
}

.hero-content-wrapper:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 163, 255, 0.2);
    border-color: rgba(0, 163, 255, 0.3);
}

/* Section Content */
.section-content {
    margin-bottom: 40px;
}

.section-title {
    font-size: 2.8rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
    background: linear-gradient(135deg,
            var(--primary) 0%,
            var(--accent) 50%,
            var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 2px;
    animation: titleUnderline 2s ease-in-out infinite alternate;
}

@keyframes titleUnderline {
    0% {
        width: 60px;
        opacity: 0.7;
    }

    100% {
        width: 80px;
        opacity: 1;
    }
}

.main-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
    font-weight: 400;
}

.secondary-description {
    font-size: 1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.75);
    font-weight: 300;
}

/* Circular Features */
.circular-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-circle {
    position: relative;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.4s ease;
    overflow: hidden;
}

.feature-circle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.05),
            rgba(0, 255, 209, 0.03));
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-circle:hover::before {
    opacity: 1;
}

.feature-circle:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.15);
    border-color: rgba(0, 163, 255, 0.3);
}

/* Feature Decorations */
.feature-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.orbital-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    border: 1px solid rgba(0, 163, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: rotate 10s linear infinite;
}

.particle-field {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 0.1) 0%,
            transparent 70%);
    border-radius: 50%;
    animation: pulse 3s ease-in-out infinite;
}

.glow-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle,
            rgba(0, 163, 255, 0.4),
            transparent);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes rotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.3;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.6;
    }
}

@keyframes glow {
    0% {
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(1);
    }

    100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

/* Feature Content */
.feature-content {
    position: relative;
    z-index: 2;
}

.feature-icon-wrapper {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 163, 255, 0.1);
    border-radius: 16px;
    border: 1px solid rgba(0, 163, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-circle:hover .feature-icon-wrapper {
    background: rgba(0, 163, 255, 0.2);
    border-color: rgba(0, 163, 255, 0.4);
    transform: scale(1.1);
}

.icon-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.1),
            rgba(0, 255, 209, 0.1));
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-circle:hover .icon-background {
    opacity: 1;
}

.feature-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 8px;
    line-height: 1.3;
}

.feature-desc {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
    font-weight: 400;
}

/* Hero Logo Column */
.hero-logo-column {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-logo-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Logo Background Effects */
.logo-background-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.energy-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50%;
    animation: ringPulse 4s ease-in-out infinite;
}

.ring-1 {
    width: 200px;
    height: 200px;
    top: -100px;
    left: -100px;
    animation-delay: 0s;
}

.ring-2 {
    width: 300px;
    height: 300px;
    top: -150px;
    left: -150px;
    animation-delay: -1.3s;
}

.ring-3 {
    width: 400px;
    height: 400px;
    top: -200px;
    left: -200px;
    animation-delay: -2.6s;
}

@keyframes ringPulse {

    0%,
    100% {
        opacity: 0.2;
        transform: scale(1);
        border-color: rgba(0, 163, 255, 0.2);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.1);
        border-color: rgba(0, 255, 209, 0.4);
    }
}

.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 30%, rgba(0, 163, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(0, 255, 209, 0.08) 0%, transparent 50%);
    animation: particleFloat 6s ease-in-out infinite alternate;
}

@keyframes particleFloat {
    0% {
        opacity: 0.3;
        transform: rotate(0deg);
    }

    100% {
        opacity: 0.7;
        transform: rotate(10deg);
    }
}

/* Main Logo */
.main-logo {
    position: relative;
    z-index: 5;
    width: 250px;
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 30px rgba(0, 163, 255, 0.4));
    transition: all 0.4s ease;
    animation: logoFloat 6s ease-in-out infinite;
}

.main-logo:hover .logo-svg {
    filter: drop-shadow(0 0 50px rgba(0, 255, 209, 0.6));
    transform: scale(1.05) rotate(5deg);
}

@keyframes logoFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
    }

    50% {
        transform: translateY(-10px) rotate(2deg);
    }
}

/* Logo Paths */
.logo-main-path {
    animation: pathGlow 3s ease-in-out infinite alternate;
}

@keyframes pathGlow {
    0% {
        filter: drop-shadow(0 0 10px rgba(0, 163, 255, 0.5));
    }

    100% {
        filter: drop-shadow(0 0 20px rgba(0, 255, 209, 0.8));
    }
}

.logo-center-circle {
    animation: circleRotate 8s linear infinite;
}

@keyframes circleRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Orbital Elements */
.orbital-elements {
    animation: orbitRotate 15s linear infinite;
}

@keyframes orbitRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.orbit-dot {
    animation: dotPulse 2s ease-in-out infinite;
}

.orbit-dot-1 {
    animation-delay: 0s;
}

.orbit-dot-2 {
    animation-delay: -0.5s;
}

.orbit-dot-3 {
    animation-delay: -1s;
}

.orbit-dot-4 {
    animation-delay: -1.5s;
}

@keyframes dotPulse {

    0%,
    100% {
        opacity: 0.6;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.3);
    }
}

/* Logo Text Overlay */
.logo-text-overlay {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 6;
}

.company-name {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 8px;
    text-shadow: 0 0 30px rgba(0, 163, 255, 0.3);
    animation: textGlow 4s ease-in-out infinite alternate;
}

@keyframes textGlow {
    0% {
        text-shadow: 0 0 30px rgba(0, 163, 255, 0.3);
    }

    100% {
        text-shadow: 0 0 50px rgba(0, 255, 209, 0.5);
    }
}

.company-tagline {
    font-size: 1.2rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 4px;
    text-transform: uppercase;
    animation: taglineFloat 3s ease-in-out infinite alternate;
}

@keyframes taglineFloat {
    0% {
        opacity: 0.8;
        transform: translateY(0);
    }

    100% {
        opacity: 1;
        transform: translateY(-3px);
    }
}

/* Responsive Design - Large Desktop */
@media (min-width: 1400px) {
    .hero-grid {
        gap: 100px;
    }

    .section-title {
        font-size: 3.2rem;
    }

    .main-description {
        font-size: 1.2rem;
    }

    .main-logo {
        width: 300px;
        height: 300px;
    }

    .company-name {
        font-size: 3.5rem;
    }
}

/* Responsive Design - Tablet */
@media (max-width: 1024px) {
    .hero-section {
        padding: 100px 0 60px;
        min-height: 90vh;
    }

    .hero-content {
        padding: 0 30px;
    }

    .hero-grid {
        gap: 60px;
        min-height: 60vh;
    }

    .hero-content-wrapper {
        padding: 40px 30px;
    }

    .section-title {
        font-size: 2.4rem;
    }

    .main-description {
        font-size: 1rem;
    }

    .circular-features {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 25px;
    }

    .feature-circle {
        padding: 25px 15px;
    }

    .main-logo {
        width: 200px;
        height: 200px;
    }

    .company-name {
        font-size: 2.5rem;
    }

    .company-tagline {
        font-size: 1rem;
        letter-spacing: 3px;
    }

    .shape-1 {
        width: 150px;
        height: 150px;
    }

    .shape-2 {
        width: 120px;
        height: 120px;
    }

    .shape-3 {
        width: 80px;
        height: 80px;
    }
}

/* Responsive Design - Mobile */
@media (max-width: 768px) {
    .hero-section {
        padding: 80px 0 50px;
        min-height: 100vh;
    }

    .hero-content {
        padding: 0 20px;
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }

    .hero-text-column {
        order: 2;
    }

    .hero-logo-column {
        order: 1;
    }

    .hero-content-wrapper {
        padding: 35px 25px;
        border-radius: 20px;
    }

    .section-title {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .section-title::after {
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
    }

    .main-description {
        font-size: 0.95rem;
        margin-bottom: 16px;
    }

    .secondary-description {
        font-size: 0.9rem;
    }

    .circular-features {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 30px;
    }

    .feature-circle {
        padding: 20px 15px;
        border-radius: 16px;
    }

    .feature-icon-wrapper {
        width: 50px;
        height: 50px;
        margin-bottom: 16px;
    }

    .feature-title {
        font-size: 1rem;
        margin-bottom: 6px;
    }

    .feature-desc {
        font-size: 0.85rem;
    }

    .main-logo {
        width: 180px;
        height: 180px;
    }

    .company-name {
        font-size: 2.2rem;
    }

    .company-tagline {
        font-size: 0.9rem;
        letter-spacing: 2px;
    }

    .logo-text-overlay {
        bottom: -50px;
    }

    .shape {
        display: none;
    }

    .ring-1 {
        width: 150px;
        height: 150px;
        top: -75px;
        left: -75px;
    }

    .ring-2 {
        width: 200px;
        height: 200px;
        top: -100px;
        left: -100px;
    }

    .ring-3 {
        width: 250px;
        height: 250px;
        top: -125px;
        left: -125px;
    }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
    .hero-section {
        padding: 70px 0 40px;
    }

    .hero-content {
        padding: 0 15px;
    }

    .hero-grid {
        gap: 40px;
    }

    .hero-content-wrapper {
        padding: 30px 20px;
        border-radius: 16px;
    }

    .section-title {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .main-description {
        font-size: 0.9rem;
        line-height: 1.6;
    }

    .secondary-description {
        font-size: 0.85rem;
    }

    .circular-features {
        gap: 16px;
        margin-top: 25px;
    }

    .feature-circle {
        padding: 18px 12px;
    }

    .feature-icon-wrapper {
        width: 45px;
        height: 45px;
        margin-bottom: 12px;
    }

    .feature-title {
        font-size: 0.95rem;
    }

    .feature-desc {
        font-size: 0.8rem;
    }

    .main-logo {
        width: 150px;
        height: 150px;
    }

    .company-name {
        font-size: 1.8rem;
    }

    .company-tagline {
        font-size: 0.8rem;
        letter-spacing: 1px;
    }

    .logo-text-overlay {
        bottom: -40px;
    }
}

/* RTL Support */
.rtl .hero-grid {
    direction: rtl;
}

.rtl .section-title::after {
    left: auto;
    right: 0;
}

.rtl .hero-text-column {
    text-align: right;
}

@media (max-width: 768px) {
    .rtl .hero-text-column {
        text-align: center;
    }

    .rtl .section-title::after {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .hero-section *,
    .hero-section *::before,
    .hero-section *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .hero-section {
        background: #000;
    }

    .hero-content-wrapper {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--primary);
    }

    .section-title {
        -webkit-text-fill-color: var(--primary);
    }

    .main-description,
    .secondary-description {
        color: #fff;
    }

    .feature-circle {
        background: rgba(255, 255, 255, 0.05);
        border-color: var(--primary);
    }
}