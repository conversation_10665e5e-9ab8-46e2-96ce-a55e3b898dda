/* ===================================
   INOVA ENERGY PROJECTS SECTION - FIXED
   Professional & Clean Design
   =================================== */

/* Projects Section Base */
.projects-section-fixed {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg,
            rgba(0, 31, 63, 1) 0%,
            rgba(0, 15, 30, 0.98) 50%,
            rgba(0, 31, 63, 1) 100%);
    overflow: hidden;
}

/* Background */
.projects-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 20%, rgba(0, 163, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(0, 255, 209, 0.08) 0%, transparent 50%);
    animation: gradientShift 20s ease-in-out infinite alternate;
}

@keyframes gradientShift {
    0% {
        opacity: 0.7;
        transform: scale(1) rotate(0deg);
    }

    100% {
        opacity: 1;
        transform: scale(1.02) rotate(1deg);
    }
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 40px 60px, rgba(0, 163, 255, 0.2), transparent),
        radial-gradient(1px 1px at 80px 120px, rgba(0, 255, 209, 0.15), transparent);
    background-size: 120px 120px;
    animation: patternMove 30s linear infinite;
    opacity: 0.3;
}

@keyframes patternMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-120px, -120px);
    }
}

/* Projects Container */
.projects-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.projects-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 24px;
    font-size: 0.9rem;
    color: var(--accent);
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.1rem;
}

.section-title {
    margin-bottom: 24px;
}

.title-main {
    display: block;
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-light);
    line-height: 1.1;
    margin-bottom: 8px;
}

.title-sub {
    display: block;
    font-size: 1.8rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.section-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
}

/* Projects Grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 80px;
}

/* Project Card */
.project-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    position: relative;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 163, 255, 0.1), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.project-card:hover::before {
    left: 100%;
}

.project-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.15);
}

.project-card.featured {
    grid-column: span 2;
}

/* Project Image */
.project-image {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.1),
            rgba(0, 255, 209, 0.08));
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.project-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.project-card:hover .project-icon {
    transform: scale(1.1) rotate(5deg);
    background: rgba(255, 255, 255, 0.15);
}

.project-icon svg {
    width: 40px;
    height: 40px;
    color: var(--accent);
    stroke-width: 2;
}

.project-category {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 163, 255, 0.2);
    color: var(--accent);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 163, 255, 0.3);
}

/* Project Content */
.project-content {
    padding: 30px;
    position: relative;
    z-index: 2;
}

.project-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 12px;
    line-height: 1.3;
}

.project-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
}

/* Project Stats */
.project-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 60px;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Project Tags */
.project-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.tag:hover {
    background: rgba(0, 163, 255, 0.1);
    color: var(--accent);
    border-color: rgba(0, 163, 255, 0.3);
}

/* Projects CTA */
.projects-cta {
    text-align: center;
    padding: 60px 40px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 25px;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.projects-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center,
            rgba(0, 163, 255, 0.1) 0%,
            transparent 70%);
    animation: ctaPulse 4s ease-in-out infinite alternate;
}

@keyframes ctaPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    text-decoration: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 163, 255, 0.4);
    gap: 16px;
}

.cta-button svg {
    width: 18px;
    height: 18px;
    stroke-width: 2;
    transition: transform 0.3s ease;
}

.cta-button:hover svg {
    transform: translateX(4px);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .projects-container {
        padding: 0 30px;
    }

    .projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .project-card.featured {
        grid-column: span 2;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .project-content {
        padding: 25px;
    }

    .project-icon {
        width: 70px;
        height: 70px;
    }

    .project-icon svg {
        width: 35px;
        height: 35px;
    }

    .project-title {
        font-size: 1.3rem;
    }

    .projects-cta {
        padding: 50px 30px;
    }

    .cta-title {
        font-size: 1.8rem;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .projects-section-fixed {
        padding: 60px 0;
    }

    .projects-container {
        padding: 0 20px;
    }

    .projects-header {
        margin-bottom: 50px;
    }

    .header-badge {
        padding: 6px 16px;
        font-size: 0.85rem;
    }

    .title-main {
        font-size: 2rem;
        line-height: 1.2;
    }

    .title-sub {
        font-size: 1.3rem;
    }

    .section-description {
        font-size: 1rem;
        max-width: 100%;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 50px;
    }

    .project-card.featured {
        grid-column: span 1;
    }

    .project-image {
        height: 160px;
    }

    .project-icon {
        width: 60px;
        height: 60px;
    }

    .project-icon svg {
        width: 30px;
        height: 30px;
    }

    .project-category {
        top: 12px;
        right: 12px;
        padding: 4px 10px;
        font-size: 0.75rem;
    }

    .project-content {
        padding: 20px;
    }

    .project-title {
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .project-description {
        font-size: 0.95rem;
        margin-bottom: 16px;
    }

    .project-stats {
        gap: 16px;
        margin-bottom: 16px;
        justify-content: center;
    }

    .stat {
        min-width: 50px;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .project-tags {
        gap: 6px;
        justify-content: center;
    }

    .tag {
        padding: 3px 10px;
        font-size: 0.75rem;
    }

    .projects-cta {
        padding: 40px 20px;
        border-radius: 20px;
    }

    .cta-title {
        font-size: 1.5rem;
        margin-bottom: 12px;
    }

    .cta-description {
        font-size: 1rem;
        margin-bottom: 24px;
    }

    .cta-button {
        padding: 14px 28px;
        font-size: 0.95rem;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .projects-section-fixed {
        padding: 50px 0;
    }

    .projects-container {
        padding: 0 15px;
    }

    .projects-header {
        margin-bottom: 40px;
    }

    .title-main {
        font-size: 1.8rem;
    }

    .title-sub {
        font-size: 1.1rem;
    }

    .section-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .projects-grid {
        gap: 16px;
        margin-bottom: 40px;
    }

    .project-image {
        height: 140px;
    }

    .project-icon {
        width: 50px;
        height: 50px;
    }

    .project-icon svg {
        width: 25px;
        height: 25px;
    }

    .project-content {
        padding: 16px;
    }

    .project-title {
        font-size: 1.1rem;
    }

    .project-description {
        font-size: 0.9rem;
    }

    .project-stats {
        gap: 12px;
    }

    .stat-number {
        font-size: 1.2rem;
    }

    .projects-cta {
        padding: 30px 16px;
    }

    .cta-title {
        font-size: 1.3rem;
    }

    .cta-description {
        font-size: 0.95rem;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}

/* RTL Support */
.rtl .projects-grid {
    direction: rtl;
}

.rtl .project-category {
    right: auto;
    left: 16px;
}

.rtl .project-stats {
    direction: rtl;
}

.rtl .project-tags {
    direction: rtl;
}

.rtl .cta-button {
    flex-direction: row-reverse;
}

.rtl .cta-button:hover svg {
    transform: translateX(-4px);
}

@media (max-width: 767px) {
    .rtl .project-category {
        left: 12px;
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .projects-section-fixed *,
    .projects-section-fixed *::before,
    .projects-section-fixed *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .projects-section-fixed {
        background: #000;
    }

    .project-card,
    .projects-cta {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .project-description,
    .stat-label,
    .cta-description {
        color: #fff;
    }
}