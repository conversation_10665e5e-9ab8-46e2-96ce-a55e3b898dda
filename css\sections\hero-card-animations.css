/* Card Animation Effects */
.hero-description-wrapper {
    transform-style: preserve-3d;
    transition: all 0.3s ease;
}

/* Energy Lines Effect */
.energy-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    border-radius: inherit;
    pointer-events: none;
}

.energy-lines::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background: linear-gradient(
        45deg,
        transparent 45%,
        rgba(var(--primary-rgb), 0.1) 50%,
        transparent 55%
    );
    animation: energyFlow 3s linear infinite;
    transform: scale(2);
}

/* Animated Background */
.animated-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    border-radius: inherit;
    z-index: 0;
}

.animated-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 50% 50%,
        rgba(var(--primary-rgb), 0.1),
        transparent 70%
    );
    animation: pulseBackground 4s ease-in-out infinite;
}

/* Animations */
@keyframes energyFlow {
    0% {
        transform: translateX(-30%) translateY(-20%) rotate(0deg);
    }
    100% {
        transform: translateX(30%) translateY(20%) rotate(360deg);
    }
}

@keyframes pulseBackground {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

/* Hover Interactions */
.hero-description-wrapper:hover .energy-lines::before {
    animation-duration: 2s;
}

.hero-description-wrapper:hover .animated-background::after {
    animation-duration: 3s;
}

/* 3D Transform Support */
@supports (perspective: 1000px) {
    .hero-description-wrapper {
        perspective: 1000px;
    }

    .content-body {
        transform: translateZ(30px);
    }
}