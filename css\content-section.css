.content-body {
    position: relative;
    height: 100vh;
    padding: 1rem;
    overflow: hidden;
}

.animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.energy-lines {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.content-grid {
    display: grid;
    grid-template-rows: auto auto 1fr;
    gap: 3px;
    height: calc(100% - 2rem);
    max-height: 100vh;
}

.content-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 0.8rem;
    opacity: 0;
    transform: translateY(20px);
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.about-section {
    display: flex;
    gap: 3px;
    align-items: start;
}

.section-icon {
    position: relative;
    width: 32px;
    height: 32px;
    margin-right: 0.5rem;
}

.icon-svg {
    width: 100%;
    height: 100%;
    fill: var(--primary-color);
    transition: transform 0.3s ease, filter 0.3s ease;
}

.icon-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    opacity: 0;
}

.section-content {
    flex: 1;
}

.section-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    color: transparent;
}

.main-description, .secondary-description {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.3rem;
    opacity: 0;
}

.rebranding-section {
    display: flex;
    gap: 3px;
    align-items: start;
}

.reasons-grid {
    opacity: 0;
}

.reasons-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.reasons-items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3px;
}

.reason-card {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(5px);
    border-radius: 6px;
    padding: 0.6rem;
    display: flex;
    align-items: start;
    gap: 3px;
    opacity: 0;
    transform: scale(0.95);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.reason-card .reason-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}

.reason-card p {
    font-size: 0.8rem;
    line-height: 1.3;
}

/* Hover effects */
.reason-card:hover {
    background: rgba(255, 255, 255, 0.07);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.reason-card:hover .icon-svg {
    filter: brightness(1.2);
}

/* Animation elements styles */
.energy-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent,
        rgba(var(--primary-rgb), 0.5),
        transparent
    );
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    filter: blur(1px);
}
