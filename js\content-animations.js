document.addEventListener('DOMContentLoaded', () => {
    // Performance check
    const isMobile = window.innerWidth <= 768;
    const isLowPerformance = navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2;

    // Skip heavy animations on low-performance devices
    if (isLowPerformance && isMobile) {
        return;
    }

    // تنظیم تایملاین اصلی برای ورود محتوا - بهینه شده
    const aboutTimeline = anime.timeline({
        easing: 'easeOutExpo'
    });

    // انیمیشن ورود کارت اصلی - بهینه شده
    const aboutContentWrapper = document.querySelector('.about-content-wrapper');
    if (aboutContentWrapper) {
        // Use Intersection Observer for better performance
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    aboutTimeline
                        .add({
                            targets: '.about-content-wrapper',
                            translateY: [30, 0],
                            opacity: [0, 1],
                            duration: isMobile ? 600 : 1200,
                            scale: [0.95, 1],
                        })
                        .add({
                            targets: '.achievement-card',
                            scale: [0, 1],
                            opacity: [0, 1],
                            duration: isMobile ? 400 : 800,
                            delay: anime.stagger(isMobile ? 50 : 100)
                        }, '-=400');

                    // Only add complex animations on desktop
                    if (!isMobile) {
                        aboutTimeline
                            .add({
                                targets: '.globe-energy-rings .energy-ring',
                                scale: [0, 1],
                                opacity: [0, 1],
                                duration: 600,
                                delay: anime.stagger(150)
                            }, '-=400')
                            .add({
                                targets: '.connection-paths .path',
                                strokeDashoffset: [anime.setDashoffset, 0],
                                duration: 1500,
                                delay: anime.stagger(200),
                                easing: 'easeInOutSine'
                            }, '-=800');
                    }

                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        observer.observe(aboutContentWrapper);

        // Simplified hover effects for better performance
        if (!isMobile) {
            aboutContentWrapper.addEventListener('mouseenter', () => {
                anime({
                    targets: '.about-content-wrapper',
                    scale: 1.01,
                    duration: 200,
                    easing: 'easeOutQuad'
                });
            });

            aboutContentWrapper.addEventListener('mouseleave', () => {
                anime({
                    targets: '.about-content-wrapper',
                    scale: 1,
                    duration: 200,
                    easing: 'easeOutQuad'
                });
            });
        }
    }

    // Globe interaction animations
    const globeContainer = document.querySelector('.globe-container');
    if (globeContainer) {
        globeContainer.addEventListener('mouseenter', () => {
            anime({
                targets: '.globe-energy-rings .energy-ring',
                scale: 1.1,
                duration: 400,
                easing: 'easeOutElastic(1, .5)'
            });
        });

        globeContainer.addEventListener('mouseleave', () => {
            anime({
                targets: '.globe-energy-rings .energy-ring',
                scale: 1,
                duration: 400,
                easing: 'easeOutElastic(1, .5)'
            });
        });
    }

    // Floating particles animation
    const particlesContainer = document.querySelector('.floating-particles');
    if (particlesContainer) {
        const particlesCount = 20;
        for (let i = 0; i < particlesCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particlesContainer.appendChild(particle);

            anime({
                targets: particle,
                translateX: () => anime.random(-100, 100),
                translateY: () => anime.random(-100, 100),
                scale: () => anime.random(0.2, 1),
                opacity: () => anime.random(0.2, 0.8),
                duration: () => anime.random(1000, 3000),
                delay: anime.random(0, 1000),
                loop: true,
                direction: 'alternate',
                easing: 'easeInOutQuad'
            });
        }
    }
});

