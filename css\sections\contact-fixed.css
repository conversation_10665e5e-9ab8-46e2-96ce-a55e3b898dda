/* ===================================
   INOVA ENERGY CONTACT SECTION - FIXED
   Professional & Clean Design
   =================================== */

/* Contact Section Base */
.contact-section-fixed {
    position: relative;
    padding: 80px 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 1) 0%,
            rgba(0, 31, 63, 0.98) 50%,
            rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* Background */
.contact-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(0, 163, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 255, 209, 0.06) 0%, transparent 50%);
    animation: gradientPulse 15s ease-in-out infinite alternate;
}

@keyframes gradientPulse {
    0% {
        opacity: 0.6;
        transform: scale(1);
    }

    100% {
        opacity: 1;
        transform: scale(1.02);
    }
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 30px 50px, rgba(0, 163, 255, 0.2), transparent),
        radial-gradient(1px 1px at 70px 100px, rgba(0, 255, 209, 0.15), transparent);
    background-size: 100px 100px;
    animation: patternDrift 25s linear infinite;
    opacity: 0.3;
}

@keyframes patternDrift {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-100px, -100px);
    }
}

/* Contact Container */
.contact-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.contact-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 24px;
    font-size: 0.9rem;
    color: var(--accent);
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.1rem;
}

.section-title {
    margin-bottom: 24px;
}

.title-main {
    display: block;
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-light);
    line-height: 1.1;
    margin-bottom: 8px;
}

.title-sub {
    display: block;
    font-size: 1.8rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.section-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
}

/* Contact Content */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 60px;
    align-items: start;
}

/* Contact Info */
.info-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

.info-card:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 163, 255, 0.2);
    transform: translateY(-5px);
}

.info-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 30px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.info-items {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 40px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
}

.info-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 12px;
    transition: all 0.3s ease;
}

.info-item:hover .info-icon {
    transform: scale(1.1) rotate(5deg);
}

.info-icon svg {
    width: 24px;
    height: 24px;
    color: white;
    stroke-width: 2;
}

.info-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 6px;
}

.info-content p {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    margin: 0;
}

.info-content p+p {
    margin-top: 4px;
}

/* Social Links */
.social-links {
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.social-links h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 16px;
}

.social-icons {
    display: flex;
    gap: 12px;
}

.social-link {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-color: transparent;
    color: white;
    transform: translateY(-3px);
}

.social-link svg {
    width: 20px;
    height: 20px;
}

/* Contact Form */
.form-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

.form-card:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 163, 255, 0.2);
}

.form-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 30px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Form Styles */
.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 4px;
}

.required {
    color: #ff4757;
    font-size: 0.8rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 0.95rem;
    color: var(--text-light);
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(0, 163, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group select {
    cursor: pointer;
}

/* Checkbox */
.checkbox-group {
    margin-top: 10px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-color: var(--primary);
}

.checkbox-label input[type="checkbox"]:checked+.checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    color: rgba(255, 255, 255, 0.8);
}

/* Form Error */
.form-error {
    font-size: 0.8rem;
    color: #ff4757;
    margin-top: 4px;
    display: none;
}

.form-group.error .form-error {
    display: block;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #ff4757;
}

/* Submit Button */
.form-actions {
    margin-top: 10px;
}

.submit-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: inherit;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 163, 255, 0.4);
    gap: 16px;
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-icon svg {
    width: 18px;
    height: 18px;
    transition: transform 0.3s ease;
}

.submit-btn:hover .btn-icon svg {
    transform: translateX(4px);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .contact-container {
        padding: 0 30px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 50px;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .info-card,
    .form-card {
        padding: 35px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .contact-section-fixed {
        padding: 100px 0;
    }

    .contact-container {
        padding: 0 30px;
        max-width: 100%;
    }

    .contact-content {
        gap: 40px;
    }

    .info-card,
    .form-card {
        padding: 30px 25px;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-input,
    .form-textarea {
        padding: 12px 16px;
        font-size: 1rem;
    }

    .submit-btn {
        padding: 12px 30px;
        font-size: 1rem;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .contact-section-fixed {
        padding: 80px 0;
        overflow-x: hidden;
    }

    .contact-container {
        padding: 0 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .contact-header {
        margin-bottom: 50px;
        text-align: center;
    }

    .header-badge {
        padding: 6px 16px;
        font-size: 0.85rem;
        margin: 0 auto 20px;
        display: inline-block;
    }

    .title-main {
        font-size: 2rem;
        line-height: 1.2;
        text-align: center;
        margin-bottom: 10px;
    }

    .title-sub {
        font-size: 1.3rem;
        text-align: center;
        margin-bottom: 20px;
    }

    .section-description {
        font-size: 1rem;
        max-width: 100%;
        text-align: center;
        line-height: 1.6;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 30px;
        width: 100%;
    }

    .info-card,
    .form-card {
        padding: 25px 20px;
        border-radius: 15px;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        margin: 0;
    }

    .info-title,
    .form-title {
        font-size: 1.3rem;
        margin-bottom: 20px;
        text-align: center;
    }

    .info-items {
        gap: 20px;
        margin-bottom: 30px;
        width: 100%;
    }

    .info-item {
        padding: 15px 12px;
        flex-direction: column;
        text-align: center;
        width: 100%;
        box-sizing: border-box;
        gap: 12px;
    }

    .info-icon {
        width: 40px;
        height: 40px;
        margin: 0 auto;
    }

    .info-icon svg {
        width: 20px;
        height: 20px;
    }

    .info-content h4 {
        font-size: 0.95rem;
        margin-bottom: 4px;
    }

    .info-content p {
        font-size: 0.9rem;
    }

    .social-links {
        padding-top: 20px;
        text-align: center;
    }

    .social-icons {
        justify-content: center;
        gap: 10px;
    }

    .social-link {
        width: 40px;
        height: 40px;
    }

    .social-link svg {
        width: 18px;
        height: 18px;
    }

    .contact-form {
        gap: 16px;
    }

    .form-row {
        gap: 16px;
    }

    .form-group label {
        font-size: 0.85rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px 14px;
        font-size: 0.9rem;
    }

    .form-group textarea {
        min-height: 100px;
    }

    .checkbox-label {
        font-size: 0.85rem;
        gap: 10px;
    }

    .checkmark {
        width: 18px;
        height: 18px;
    }

    .submit-btn {
        width: 100%;
        justify-content: center;
        padding: 14px 28px;
        font-size: 0.95rem;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .contact-section-fixed {
        padding: 50px 0;
    }

    .contact-container {
        padding: 0 15px;
    }

    .contact-header {
        margin-bottom: 40px;
    }

    .title-main {
        font-size: 1.8rem;
    }

    .title-sub {
        font-size: 1.1rem;
    }

    .section-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .contact-content {
        gap: 30px;
    }

    .info-card,
    .form-card {
        padding: 20px 16px;
    }

    .info-title,
    .form-title {
        font-size: 1.2rem;
    }

    .info-items {
        gap: 16px;
    }

    .info-item {
        padding: 10px;
    }

    .info-icon {
        width: 36px;
        height: 36px;
    }

    .info-icon svg {
        width: 18px;
        height: 18px;
    }

    .info-content h4 {
        font-size: 0.9rem;
    }

    .info-content p {
        font-size: 0.85rem;
    }

    .social-icons {
        gap: 8px;
    }

    .social-link {
        width: 36px;
        height: 36px;
    }

    .social-link svg {
        width: 16px;
        height: 16px;
    }

    .contact-form {
        gap: 14px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 9px 12px;
        font-size: 0.85rem;
    }

    .submit-btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}

/* RTL Support */
.rtl .contact-content {
    direction: rtl;
}

.rtl .info-item {
    flex-direction: row-reverse;
}

.rtl .info-item:hover {
    transform: translateX(-5px);
}

.rtl .checkbox-label {
    flex-direction: row-reverse;
}

.rtl .submit-btn {
    flex-direction: row-reverse;
}

.rtl .submit-btn:hover .btn-icon svg {
    transform: translateX(-4px);
}

@media (max-width: 767px) {
    .rtl .info-item {
        flex-direction: column;
    }

    .rtl .info-item:hover {
        transform: translateY(-2px);
    }

    .rtl .checkbox-label {
        flex-direction: row;
        text-align: right;
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .contact-section-fixed *,
    .contact-section-fixed *::before,
    .contact-section-fixed *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .contact-section-fixed {
        background: #000;
    }

    .info-card,
    .form-card {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .info-content p,
    .checkbox-text,
    .section-description {
        color: #fff;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }
}