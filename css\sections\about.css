/* --- سکشن درباره ما: بازطراحی ویژه شرکت انرژی --- */
.about-section-modern {
  position: relative;
  padding: 64px 0 48px 0;
  background: linear-gradient(135deg, #0a223a 0%, #0e2d47 100%);
  overflow: hidden;
  z-index: 1;
  width: 100vw;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.about-hero-bg {
  position: absolute;
  inset: 0;
  background: radial-gradient(ellipse at 70% 10%, #00e6ff33 0%, transparent 70%),
              radial-gradient(ellipse at 20% 80%, #00ffc633 0%, transparent 70%);
  z-index: 0;
  pointer-events: none;
}

/* کانتینر اصلی */
.container.about-flex {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1800px;
  margin: 0 auto;
  padding: 0 2vw;
  display: flex;
  flex-direction: column;
  gap: 48px;
  box-sizing: border-box;
}

/* مقدمه درباره ما */
.about-intro {
  text-align: center;
  margin-bottom: 32px;
}
.about-title.gradient-text {
  font-size: 2.4rem;
  font-weight: 800;
  background: linear-gradient(90deg, #00e6ff 0%, #00ffc6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12px;
  letter-spacing: -1px;
}
.about-lead-modern {
  font-size: 1.2rem;
  color: #e0f7fa;
  margin-bottom: 8px;
}
.about-highlight {
  color: #00ffc6;
  font-weight: 700;
}
.about-desc-modern {
  color: #b2ebf2;
  font-size: 1rem;
  margin-bottom: 0;
}

/* گرید اصلی: سه ستون کنار هم */
.about-cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  width: 100%;
  align-items: stretch;
}

/* هر ستون */
.about-values-modern,
.about-mission-modern,
.about-timeline-modern {
  background: rgba(255,255,255,0.04);
  border-radius: 18px;
  box-shadow: 0 4px 32px 0 #00e6ff11;
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-sizing: border-box;
  min-width: 0;
  width: 100%;
  height: 100%;
  /* برای هم‌ارتفاع شدن ستون‌ها */
  justify-content: flex-start;
}

/* تایم‌لاین مدرن */
.about-timeline-modern {
  /* تایم‌لاین را هم‌تراز و هم‌ارتفاع با بقیه ستون‌ها قرار می‌دهد */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

/* ارزش‌ها */
.values-title {
  font-size: 1.25rem;
  color: #00e6ff;
  font-weight: 700;
  margin-bottom: 18px;
  letter-spacing: -0.5px;
}
.values-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
  width: 100%;
}
.value-modern {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  background: rgba(0,255,198,0.07);
  border-radius: 12px;
  padding: 14px 12px;
  transition: box-shadow 0.2s;
  box-shadow: 0 2px 12px 0 #00ffc611;
}
.value-modern:hover {
  box-shadow: 0 4px 24px 0 #00ffc633;
  background: rgba(0,255,198,0.13);
}
.value-modern h4 {
  font-size: 1.08rem;
  color: #00ffc6;
  margin: 0 0 2px 0;
  font-weight: 700;
}
.value-modern p {
  color: #e0f7fa;
  font-size: 0.97rem;
  margin: 0;
}

/* ماموریت و چشم‌انداز */
.mission-title {
  font-size: 1.25rem;
  color: #00e6ff;
  font-weight: 700;
  margin-bottom: 18px;
  letter-spacing: -0.5px;
}
.mission-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
  width: 100%;
}
.mission-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: rgba(0,230,255,0.07);
  border-radius: 12px;
  padding: 14px 12px;
  transition: box-shadow 0.2s;
  box-shadow: 0 2px 12px 0 #00e6ff11;
}
.mission-item:hover {
  box-shadow: 0 4px 24px 0 #00e6ff33;
  background: rgba(0,230,255,0.13);
}
.mission-item h5 {
  color: #00e6ff;
  font-size: 1.05rem;
  margin: 0 0 2px 0;
  font-weight: 700;
}
.mission-item p {
  color: #b2ebf2;
  font-size: 0.97rem;
  margin: 0;
}

/* تایم‌لاین مدرن */
.timeline-title {
  color: #00ffc6;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 18px;
  text-align: center;
}
.timeline-modern {
  display: flex;
  flex-direction: column;
  gap: 18px;
  width: 100%;
}
.timeline-event {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 10px 0;
  border-bottom: 1px solid rgba(0,255,198,0.08);
  position: relative;
}
.timeline-event:last-child {
  border-bottom: none;
}
.timeline-year {
  color: #00e6ff;
  font-weight: 700;
  font-size: 1.05rem;
  min-width: 54px;
  text-align: center;
}
.timeline-dot {
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #00e6ff 0%, #00ffc6 100%);
  border-radius: 50%;
  box-shadow: 0 0 8px #00ffc6cc;
  margin-left: 6px;
  margin-right: 6px;
}
.timeline-desc {
  color: #e0f7fa;
  font-size: 0.97rem;
  flex: 1;
}

/* ریسپانسیو ویژه */
@media (max-width: 1200px) {
  .about-cards-grid {
    grid-template-columns: 1fr 1fr;
  }
  .about-timeline-modern {
    grid-column: span 2;
    margin-top: 32px;
  }
}
@media (max-width: 900px) {
  .about-cards-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .about-values-modern,
  .about-mission-modern,
  .about-timeline-modern {
    min-width: 0;
    width: 100%;
    margin: 0;
  }
  .about-timeline-modern {
    margin-top: 24px;
    grid-column: auto;
  }
}
@media (max-width: 700px) {
  .about-section-modern {
    padding: 36px 0 24px 0;
  }
  .about-title.gradient-text {
    font-size: 1.5rem;
  }
  .about-cards-grid {
    gap: 16px;
  }
  .about-values-modern,
  .about-mission-modern,
  .about-timeline-modern {
    padding: 18px 8px;
    border-radius: 12px;
  }
  .timeline-title {
    font-size: 1rem;
  }
}
@media (max-width: 480px) {
  .about-section-modern {
    padding: 18px 0 10px 0;
    margin-left: -8vw;
    margin-right: -8vw;
    width: 116vw;
  }
  .container.about-flex {
    padding: 0 2vw;
    gap: 24px;
  }
  .about-title.gradient-text {
    font-size: 1.1rem;
  }
  .about-lead-modern,
  .about-desc-modern {
    font-size: 0.93rem;
  }
  .timeline-event {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    padding: 8px 0;
  }
  .timeline-year {
    min-width: 0;
    font-size: 0.97rem;
  }
}
