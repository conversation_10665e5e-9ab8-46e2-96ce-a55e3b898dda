document.addEventListener('DOMContentLoaded', () => {
    const header = document.querySelector('.site-header') || document.querySelector('.main-header');
    const navContainer = document.querySelector('.nav-container');
    const ctaButton = document.querySelector('.cta-button');

    // Scroll Handler
    let lastScroll = 0;
    const scrollThreshold = 100;

    window.addEventListener('scroll', () => {
        const currentScroll = window.pageYOffset;

        // Add/remove scrolled class
        if (header) {
            if (currentScroll > scrollThreshold) {
                header.classList.add('header-scrolled');
            } else {
                header.classList.remove('header-scrolled');
            }

            // Hide/show header on scroll
            if (currentScroll > lastScroll && currentScroll > scrollThreshold) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }
        }

        lastScroll = currentScroll;
    });

    // Particle Animation for CTA Button
    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'button-particle';

        const size = Math.random() * 3 + 1;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        const startX = Math.random() * 100;
        const startY = Math.random() * 100;
        particle.style.left = `${startX}%`;
        particle.style.top = `${startY}%`;

        return particle;
    }

    function initParticles() {
        const ctaButton = document.querySelector('.cta-button');
        if (!ctaButton) {
            console.warn('CTA button not found');
            return;
        }

        const particlesContainer = ctaButton.querySelector('.btn-particles');
        if (!particlesContainer) {
            // Create particles container if it doesn't exist
            const newParticlesContainer = document.createElement('div');
            newParticlesContainer.className = 'btn-particles';
            ctaButton.appendChild(newParticlesContainer);
            return;
        }

        for (let i = 0; i < 15; i++) {
            particlesContainer.appendChild(createParticle());
        }
    }

    initParticles();

    // Active Link Indicator
    const navLinks = document.querySelectorAll('.nav-link');

    function updateActiveLink() {
        const scrollPosition = window.scrollY;

        navLinks.forEach(link => {
            const sectionId = link.getAttribute('href');
            const section = document.querySelector(sectionId);

            if (section) {
                const sectionTop = section.offsetTop - 100;
                const sectionBottom = sectionTop + section.offsetHeight;

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            }
        });
    }

    window.addEventListener('scroll', updateActiveLink);
    updateActiveLink();
});


