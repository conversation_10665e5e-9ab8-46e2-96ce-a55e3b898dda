/* Unified styles for the Projects section, including smooth transitions and dynamic effects */

.projects-section {
    padding: 60px 20px;
    background-color: #f9f9f9;
    position: relative;
    overflow: hidden;
}

.projects-section h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 40px;
    color: #333;
    transition: color 0.3s ease;
}

.projects-section h2:hover {
    color: #007bff;
}

.project-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 20px;
    padding: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.project-card img {
    max-width: 100%;
    border-radius: 10px;
}

.project-card h3 {
    font-size: 1.5rem;
    margin: 15px 0;
    color: #333;
}

.project-card p {
    font-size: 1rem;
    color: #666;
}

.project-card a {
    display: inline-block;
    margin-top: 15px;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.project-card a:hover {
    background-color: #0056b3;
}

/* Smooth scrolling effect */
html {
    scroll-behavior: smooth;
}

/* Animation for section transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.projects-section {
    animation: fadeIn 0.8s ease forwards;
}