<?php

/**
 * INOVA Energy Theme Functions
 * 
 * @package INOVA_Energy
 * @version 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include security functions
if (function_exists('get_template_directory')) {
    require_once get_template_directory() . '/security.php';
} else {
    require_once __DIR__ . '/security.php';
}

// Theme setup
function inova_energy_setup()
{
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));
    add_theme_support('customize-selective-refresh-widgets');
    add_theme_support('wp-block-styles');
    add_theme_support('align-wide');

    // Load text domain for translations
    load_theme_textdomain('inova-energy', get_template_directory() . '/languages');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'inova-energy'),
        'footer' => __('Footer Menu', 'inova-energy'),
    ));
}
add_action('after_setup_theme', 'inova_energy_setup');

// Enqueue styles and scripts
function inova_energy_scripts()
{
    // Get current language
    $current_lang = get_current_language();

    // Main theme stylesheet
    wp_enqueue_style('inova-energy-style', get_stylesheet_uri(), array(), '1.0');

    // Google Fonts
    if ($current_lang === 'fa') {
        wp_enqueue_style('vazirmatn-font', 'https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700;800&display=swap');
    } else {
        wp_enqueue_style('inter-font', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
    }

    // Component styles
    wp_enqueue_style('inova-base-styles', get_template_directory_uri() . '/css/base/styles.css', array(), '1.0');
    wp_enqueue_style('inova-header', get_template_directory_uri() . '/css/components/header.css', array(), '1.0');
    wp_enqueue_style('inova-header-mobile-ultimate', get_template_directory_uri() . '/css/components/header-mobile-ultimate.css', array(), '1.0');
    wp_enqueue_style('inova-animations', get_template_directory_uri() . '/css/components/animations.css', array(), '1.0');
    wp_enqueue_style('inova-responsive', get_template_directory_uri() . '/css/components/responsive.css', array(), '1.0');
    wp_enqueue_style('inova-responsive-master', get_template_directory_uri() . '/css/components/responsive-master.css', array(), '1.0');
    wp_enqueue_style('inova-mobile-ultimate', get_template_directory_uri() . '/css/components/mobile-ultimate.css', array(), '1.0');
    wp_enqueue_style('inova-mobile-scroll-fix', get_template_directory_uri() . '/css/components/mobile-scroll-fix.css', array(), '1.0');
    wp_enqueue_style('inova-desktop-responsive-fix', get_template_directory_uri() . '/css/components/desktop-responsive-fix.css', array(), '1.0');
    wp_enqueue_style('inova-multilingual', get_template_directory_uri() . '/css/components/multilingual.css', array(), '1.0');

    // RTL/LTR specific styles
    wp_enqueue_style('inova-rtl-support', get_template_directory_uri() . '/css/components/rtl-support.css', array(), '1.0');
    wp_enqueue_style('inova-scroll-fix', get_template_directory_uri() . '/css/components/scroll-fix.css', array(), '1.0');
    wp_enqueue_style('inova-header-professional', get_template_directory_uri() . '/css/components/header-professional.css', array(), '1.0');
    wp_enqueue_style('inova-final-optimizations', get_template_directory_uri() . '/css/components/final-optimizations.css', array(), '1.0');

    // Section styles - Essential only
    wp_enqueue_style('inova-hero-professional', get_template_directory_uri() . '/css/sections/hero-professional.css', array(), '1.0');
    wp_enqueue_style('inova-about-ultimate', get_template_directory_uri() . '/css/sections/about-ultimate.css', array(), '1.0');
    wp_enqueue_style('inova-services-professional', get_template_directory_uri() . '/css/sections/services-professional.css', array(), '1.0');
    wp_enqueue_style('inova-activities-professional', get_template_directory_uri() . '/css/sections/activities-professional.css', array(), '1.0');
    wp_enqueue_style('inova-rebranding-professional', get_template_directory_uri() . '/css/sections/rebranding-professional.css', array(), '1.0');
    wp_enqueue_style('inova-contact-professional', get_template_directory_uri() . '/css/sections/contact-professional.css', array(), '1.0');
    wp_enqueue_style('inova-footer', get_template_directory_uri() . '/css/sections/footer.css', array(), '1.0');

    // Third-party libraries
    wp_enqueue_style('aos-css', 'https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css');
    wp_enqueue_style('swiper-css', 'https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css');
    wp_enqueue_style('splitting-css', 'https://unpkg.com/splitting/dist/splitting.css');
    wp_enqueue_style('splitting-cells-css', 'https://unpkg.com/splitting/dist/splitting-cells.css');

    // JavaScript libraries
    wp_enqueue_script('splitting-js', 'https://unpkg.com/splitting/dist/splitting.min.js', array(), '1.0', true);
    wp_enqueue_script('aos-js', 'https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js', array(), '1.0', true);
    wp_enqueue_script('threejs', 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js', array(), '1.0', true);
    wp_enqueue_script('gsap', 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js', array(), '1.0', true);
    wp_enqueue_script('gsap-scrolltrigger', 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js', array('gsap'), '1.0', true);
    wp_enqueue_script('animejs', 'https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js', array(), '1.0', true);
    wp_enqueue_script('lordicon', 'https://cdn.lordicon.com/libs/mssddfmo/lord-icon-2.1.0.js', array(), '1.0', true);

    // Custom scripts
    wp_enqueue_script('inova-preloader', get_template_directory_uri() . '/js/preloader.js', array(), '1.0', true);
    wp_enqueue_script('inova-particles', get_template_directory_uri() . '/js/particles.js', array('threejs'), '1.0', true);
    wp_enqueue_script('inova-globe', get_template_directory_uri() . '/js/globe.js', array('threejs'), '1.0', true);
    wp_enqueue_script('inova-stats', get_template_directory_uri() . '/js/stats.js', array(), '1.0', true);
    wp_enqueue_script('inova-hero-animations', get_template_directory_uri() . '/js/hero-animations.js', array('gsap'), '1.0', true);
    wp_enqueue_script('inova-header-animations', get_template_directory_uri() . '/js/header-animations.js', array('gsap'), '1.0', true);
    wp_enqueue_script('inova-content-animations', get_template_directory_uri() . '/js/content-animations.js', array('gsap'), '1.0', true);
    wp_enqueue_script('inova-main', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0', true);
    wp_enqueue_script('inova-feature-circles', get_template_directory_uri() . '/js/feature-circles.js', array(), '1.0', true);
    wp_enqueue_script('inova-section-transitions', get_template_directory_uri() . '/js/section-transitions.js', array('gsap'), '1.0', true);
    wp_enqueue_script('inova-rebranding', get_template_directory_uri() . '/js/rebranding.js', array(), '1.0', true);

    // Language switcher script
    wp_enqueue_script('inova-language-switcher', get_template_directory_uri() . '/js/language-switcher.js', array('jquery'), '1.0', true);

    // Mobile menu ultimate
    wp_enqueue_script('inova-mobile-menu-ultimate', get_template_directory_uri() . '/js/mobile-menu-ultimate.js', array(), '1.0', true);

    // Localize script for AJAX
    wp_localize_script('inova-language-switcher', 'inova_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('inova_language_nonce'),
        'current_lang' => get_current_language(),
    ));
}
add_action('wp_enqueue_scripts', 'inova_energy_scripts');

// Get current language
function get_current_language()
{
    return isset($_COOKIE['inova_lang']) ? $_COOKIE['inova_lang'] : 'fa';
}

// AJAX handler for language switching
function inova_switch_language()
{
    check_ajax_referer('inova_language_nonce', 'nonce');

    $lang = sanitize_text_field($_POST['lang']);
    if (in_array($lang, array('fa', 'en'))) {
        setcookie('inova_lang', $lang, time() + (86400 * 30), '/'); // 30 days
        wp_send_json_success(array('lang' => $lang));
    } else {
        wp_send_json_error('Invalid language');
    }
}
add_action('wp_ajax_inova_switch_language', 'inova_switch_language');
add_action('wp_ajax_nopriv_inova_switch_language', 'inova_switch_language');

// Add body classes for language and direction
function inova_body_classes($classes)
{
    $current_lang = get_current_language();
    $classes[] = 'lang-' . $current_lang;
    $classes[] = 'rtl'; // Default RTL for Persian
    return $classes;
}
add_filter('body_class', 'inova_body_classes');

// Translation function
function inova_translate($key, $fa_text, $en_text)
{
    $current_lang = get_current_language();
    return ($current_lang === 'fa') ? $fa_text : $en_text;
}

// SEO Functions
function inova_seo_meta_tags()
{
    $current_lang = get_current_language();

    // SEO meta tags
    if ($current_lang === 'fa') {
        echo '<meta name="description" content="INOVA Energy - پیشگام در انرژی پایدار و فناوری‌های نوین. بیش از 25 سال تجربه در صنعت انرژی، نفت، گاز و پتروشیمی.">' . "\n";
        echo '<meta name="keywords" content="انرژی پایدار, نفت, گاز, پتروشیمی, انرژی تجدیدپذیر, INOVA Energy, شرکت انرژی">' . "\n";
    } else {
        echo '<meta name="description" content="INOVA Energy - Pioneer in sustainable energy and innovative technologies. Over 25 years of experience in energy, oil, gas and petrochemical industries.">' . "\n";
        echo '<meta name="keywords" content="sustainable energy, oil, gas, petrochemical, renewable energy, INOVA Energy, energy company">' . "\n";
    }

    // Open Graph tags
    echo '<meta property="og:type" content="website">' . "\n";
    echo '<meta property="og:url" content="' . home_url() . '">' . "\n";
    echo '<meta property="og:site_name" content="INOVA Energy">' . "\n";
    echo '<meta property="og:locale" content="' . ($current_lang === 'fa' ? 'fa_IR' : 'en_US') . '">' . "\n";

    if ($current_lang === 'fa') {
        echo '<meta property="og:title" content="INOVA Energy - پیشگام انرژی پایدار">' . "\n";
        echo '<meta property="og:description" content="پیشگام در انرژی پایدار و فناوری‌های نوین. بیش از 25 سال تجربه در صنعت انرژی.">' . "\n";
    } else {
        echo '<meta property="og:title" content="INOVA Energy - Sustainable Energy Pioneer">' . "\n";
        echo '<meta property="og:description" content="Pioneer in sustainable energy and innovative technologies. Over 25 years of experience in energy industry.">' . "\n";
    }

    echo '<meta property="og:image" content="' . get_template_directory_uri() . '/images/logo1.png">' . "\n";

    // Twitter Card tags
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    echo '<meta name="twitter:site" content="@inovaenergy">' . "\n";

    // Additional SEO tags
    echo '<meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">' . "\n";
    echo '<link rel="canonical" href="' . home_url() . '">' . "\n";

    // Language alternates
    echo '<link rel="alternate" hreflang="fa" href="' . home_url() . '?lang=fa">' . "\n";
    echo '<link rel="alternate" hreflang="en" href="' . home_url() . '?lang=en">' . "\n";
    echo '<link rel="alternate" hreflang="x-default" href="' . home_url() . '">' . "\n";
}

function inova_structured_data()
{
    $current_lang = get_current_language();

    $structured_data = array(
        '@context' => 'https://schema.org',
        '@type' => 'Organization',
        'name' => 'INOVA Energy',
        'url' => home_url(),
        'logo' => get_template_directory_uri() . '/images/logo1.png',
        'description' => $current_lang === 'fa'
            ? 'پیشگام در انرژی پایدار و فناوری‌های نوین. بیش از 25 سال تجربه در صنعت انرژی، نفت، گاز و پتروشیمی.'
            : 'Pioneer in sustainable energy and innovative technologies. Over 25 years of experience in energy, oil, gas and petrochemical industries.',
        'foundingDate' => '1999',
        'industry' => 'Energy',
        'numberOfEmployees' => '500+',
        'contactPoint' => array(
            '@type' => 'ContactPoint',
            'contactType' => 'customer service',
            'email' => '<EMAIL>',
            'availableLanguage' => array('Persian', 'English')
        )
    );

    echo '<script type="application/ld+json">' . json_encode($structured_data, JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
}

// Initialize AOS on page load
function inova_init_aos()
{
    echo '<script>
        document.addEventListener("DOMContentLoaded", function() {
            AOS.init({
                duration: 1000,
                once: true
            });
        });
    </script>';
}

// Security is handled by security.php file

// Add SEO hooks
add_action('wp_head', 'inova_seo_meta_tags');
add_action('wp_head', 'inova_structured_data');
add_action('wp_footer', 'inova_init_aos');
