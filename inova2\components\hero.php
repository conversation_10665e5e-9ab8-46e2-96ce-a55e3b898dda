<div class="hero-section" id="hero">
    <div class="hero-content">
        <h1 class="hero-title">Welcome to INOVA Energy</h1>
        <p class="hero-description">پیشگام در صنعت انرژی پایدار</p>
        <a href="#about" class="hero-button">Learn More</a>
    </div>
    <div class="hero-image">
        <img src="images/hero-image.png" alt="Hero Image" class="hero-img">
    </div>
</div>

<style>
    .hero-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 100px 50px;
        background: linear-gradient(to right, #4a90e2, #50e3c2);
        color: white;
        position: relative;
        overflow: hidden;
        transition: background 0.5s ease;
    }

    .hero-content {
        max-width: 600px;
    }

    .hero-title {
        font-size: 3rem;
        margin-bottom: 20px;
        animation: fadeIn 1s ease-in-out;
    }

    .hero-description {
        font-size: 1.5rem;
        margin-bottom: 30px;
        animation: fadeIn 1.5s ease-in-out;
    }

    .hero-button {
        padding: 10px 20px;
        background-color: #f39c12;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        transition: background-color 0.3s ease;
    }

    .hero-button:hover {
        background-color: #e67e22;
    }

    .hero-image {
        flex: 1;
        text-align: right;
    }

    .hero-img {
        max-width: 100%;
        height: auto;
        animation: slideIn 1s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* Smooth scroll effect */
    html {
        scroll-behavior: smooth;
    }
</style>