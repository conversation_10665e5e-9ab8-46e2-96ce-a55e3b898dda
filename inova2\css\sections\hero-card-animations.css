/* Hero Card Animations */

.hero-card {
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.hero-card img {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.hero-card:hover img {
    transform: scale(1.1);
}

.hero-card-content {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    color: #fff;
    text-align: center;
    transition: opacity 0.3s ease;
}

.hero-card:hover .hero-card-content {
    opacity: 0.9;
}

/* Smooth Section Transitions */

section {
    position: relative;
    overflow: hidden;
    transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

section.hidden {
    opacity: 0;
    transform: translateY(20px);
}

section.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Media Queries for Responsiveness */

@media (max-width: 768px) {
    .hero-card {
        margin: 10px;
    }

    .hero-card-content {
        font-size: 14px;
    }
}