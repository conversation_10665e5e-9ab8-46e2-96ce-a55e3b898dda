/* ===================================
   INOVA ENERGY - HERO ENERGY RINGS ULTIMATE
   Professional Energy Ring System
   =================================== */

/* Energy Field System Base */
.energy-field-system {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    height: 600px;
    pointer-events: none;
    z-index: 1;
}

/* Energy Ring Base Styles */
.energy-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 2px solid transparent;
    background: linear-gradient(45deg,
            rgba(0, 163, 255, 0.3),
            rgba(0, 255, 209, 0.2),
            rgba(0, 163, 255, 0.1),
            transparent);
    background-clip: padding-box;
    transform: translate(-50%, -50%);
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

/* Outer Energy Rings - Largest, Slowest */
.ring-outer-1 {
    width: 580px;
    height: 580px;
    border-width: 3px;
    animation: ringRotateClockwise 35s linear infinite;
    background: conic-gradient(from 0deg,
            rgba(0, 163, 255, 0.4) 0deg,
            rgba(0, 255, 209, 0.3) 90deg,
            rgba(0, 163, 255, 0.2) 180deg,
            rgba(0, 255, 209, 0.1) 270deg,
            rgba(0, 163, 255, 0.4) 360deg);
}

.ring-outer-2 {
    width: 520px;
    height: 520px;
    border-width: 2px;
    animation: ringRotateCounterClockwise 30s linear infinite;
    background: conic-gradient(from 45deg,
            rgba(0, 255, 209, 0.35) 0deg,
            rgba(0, 163, 255, 0.25) 90deg,
            rgba(0, 255, 209, 0.15) 180deg,
            rgba(0, 163, 255, 0.05) 270deg,
            rgba(0, 255, 209, 0.35) 360deg);
}

.ring-outer-3 {
    width: 460px;
    height: 460px;
    border-width: 2px;
    animation: ringRotateClockwise 25s linear infinite;
    background: conic-gradient(from 90deg,
            rgba(0, 163, 255, 0.3) 0deg,
            rgba(0, 255, 209, 0.2) 120deg,
            rgba(0, 163, 255, 0.1) 240deg,
            rgba(0, 163, 255, 0.3) 360deg);
}

/* Middle Energy Rings - Medium Size, Medium Speed */
.ring-middle-1 {
    width: 380px;
    height: 380px;
    border-width: 3px;
    animation: ringRotateCounterClockwise 20s linear infinite;
    background: conic-gradient(from 135deg,
            rgba(0, 255, 209, 0.4) 0deg,
            rgba(0, 163, 255, 0.3) 60deg,
            rgba(0, 255, 209, 0.2) 120deg,
            rgba(0, 163, 255, 0.1) 180deg,
            rgba(0, 255, 209, 0.2) 240deg,
            rgba(0, 163, 255, 0.3) 300deg,
            rgba(0, 255, 209, 0.4) 360deg);
}

.ring-middle-2 {
    width: 320px;
    height: 320px;
    border-width: 2px;
    animation: ringRotateClockwise 18s linear infinite;
    background: conic-gradient(from 180deg,
            rgba(0, 163, 255, 0.35) 0deg,
            rgba(0, 255, 209, 0.25) 72deg,
            rgba(0, 163, 255, 0.15) 144deg,
            rgba(0, 255, 209, 0.05) 216deg,
            rgba(0, 163, 255, 0.25) 288deg,
            rgba(0, 163, 255, 0.35) 360deg);
}

/* Inner Energy Rings - Smaller, Faster */
.ring-inner-1 {
    width: 260px;
    height: 260px;
    border-width: 3px;
    animation: ringRotateCounterClockwise 15s linear infinite;
    background: conic-gradient(from 225deg,
            rgba(0, 255, 209, 0.5) 0deg,
            rgba(0, 163, 255, 0.4) 45deg,
            rgba(0, 255, 209, 0.3) 90deg,
            rgba(0, 163, 255, 0.2) 135deg,
            rgba(0, 255, 209, 0.3) 180deg,
            rgba(0, 163, 255, 0.4) 225deg,
            rgba(0, 255, 209, 0.5) 270deg,
            rgba(0, 163, 255, 0.4) 315deg,
            rgba(0, 255, 209, 0.5) 360deg);
}

.ring-inner-2 {
    width: 200px;
    height: 200px;
    border-width: 2px;
    animation: ringRotateClockwise 12s linear infinite;
    background: conic-gradient(from 270deg,
            rgba(0, 163, 255, 0.45) 0deg,
            rgba(0, 255, 209, 0.35) 40deg,
            rgba(0, 163, 255, 0.25) 80deg,
            rgba(0, 255, 209, 0.15) 120deg,
            rgba(0, 163, 255, 0.25) 160deg,
            rgba(0, 255, 209, 0.35) 200deg,
            rgba(0, 163, 255, 0.45) 240deg,
            rgba(0, 255, 209, 0.35) 280deg,
            rgba(0, 163, 255, 0.45) 320deg,
            rgba(0, 163, 255, 0.45) 360deg);
}

/* Core Energy Ring - Smallest, Fastest */
.ring-core {
    width: 140px;
    height: 140px;
    border-width: 4px;
    animation: ringRotateCounterClockwise 8s linear infinite;
    background: conic-gradient(from 315deg,
            rgba(0, 255, 209, 0.6) 0deg,
            rgba(0, 163, 255, 0.5) 30deg,
            rgba(0, 255, 209, 0.4) 60deg,
            rgba(0, 163, 255, 0.3) 90deg,
            rgba(0, 255, 209, 0.4) 120deg,
            rgba(0, 163, 255, 0.5) 150deg,
            rgba(0, 255, 209, 0.6) 180deg,
            rgba(0, 163, 255, 0.5) 210deg,
            rgba(0, 255, 209, 0.4) 240deg,
            rgba(0, 163, 255, 0.3) 270deg,
            rgba(0, 255, 209, 0.4) 300deg,
            rgba(0, 163, 255, 0.5) 330deg,
            rgba(0, 255, 209, 0.6) 360deg);
    box-shadow:
        0 0 20px rgba(0, 255, 209, 0.4),
        inset 0 0 20px rgba(0, 163, 255, 0.3);
}

/* Ring Animations */
@keyframes ringRotateClockwise {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes ringRotateCounterClockwise {
    0% {
        transform: translate(-50%, -50%) rotate(360deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
}

/* Ring Inner Effects */
.ring-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg,
            rgba(0, 163, 255, 0.2),
            rgba(0, 255, 209, 0.1));
    animation: glowPulse 3s ease-in-out infinite alternate;
}

.ring-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle at 25% 25%,
            rgba(0, 255, 209, 0.3) 0%,
            transparent 50%);
    animation: particleFloat 4s ease-in-out infinite;
}

.ring-pulse {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 50%;
    border: 1px solid rgba(0, 163, 255, 0.4);
    animation: pulseBeat 2s ease-in-out infinite;
}

.ring-energy-flow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(from 0deg,
            transparent 0deg,
            rgba(0, 255, 209, 0.3) 45deg,
            transparent 90deg,
            rgba(0, 163, 255, 0.3) 135deg,
            transparent 180deg,
            rgba(0, 255, 209, 0.3) 225deg,
            transparent 270deg,
            rgba(0, 163, 255, 0.3) 315deg,
            transparent 360deg);
    animation: energyFlow 6s linear infinite;
}

.ring-spark {
    position: absolute;
    top: -2px;
    left: 50%;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 1) 0%,
            rgba(0, 163, 255, 0.8) 50%,
            transparent 100%);
    border-radius: 50%;
    transform: translateX(-50%);
    animation: sparkGlow 1.5s ease-in-out infinite alternate;
}

.ring-rotation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(90deg,
            rgba(0, 163, 255, 0.2) 0%,
            transparent 50%,
            rgba(0, 255, 209, 0.2) 100%);
    animation: rotationEffect 3s linear infinite;
}

.core-energy {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80%;
    height: 80%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 0.4) 0%,
            rgba(0, 163, 255, 0.3) 30%,
            rgba(0, 255, 209, 0.2) 60%,
            transparent 100%);
    animation: coreEnergyPulse 2s ease-in-out infinite alternate;
}

.core-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60%;
    height: 60%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    border: 2px solid rgba(0, 255, 209, 0.6);
    animation: corePulseExpand 1.5s ease-out infinite;
}

/* Animation Keyframes */
@keyframes glowPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.02);
    }
}

@keyframes particleFloat {

    0%,
    100% {
        transform: rotate(0deg) translateX(10px) rotate(0deg);
    }

    50% {
        transform: rotate(180deg) translateX(15px) rotate(-180deg);
    }
}

@keyframes pulseBeat {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.6;
    }

    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes energyFlow {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes sparkGlow {
    0% {
        opacity: 0.5;
        transform: translateX(-50%) scale(1);
    }

    100% {
        opacity: 1;
        transform: translateX(-50%) scale(1.5);
    }
}

@keyframes rotationEffect {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes coreEnergyPulse {
    0% {
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(1);
    }

    100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes corePulseExpand {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }

    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

/* ===================================
   LOGO CENTER SYSTEM
   =================================== */

/* Logo Center Container */
.logo-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Logo Wrapper */
.logo-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Logo Background Effects */
.logo-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    z-index: 1;
}

.logo-glow-effect {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 0.3) 0%,
            rgba(0, 163, 255, 0.2) 50%,
            transparent 100%);
    animation: logoGlowPulse 3s ease-in-out infinite alternate;
}

.logo-shadow {
    position: absolute;
    top: 5px;
    left: 5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: radial-gradient(circle,
            rgba(0, 0, 0, 0.3) 0%,
            transparent 70%);
    filter: blur(8px);
}

/* Logo Image Container */
.logo-image-container {
    position: relative;
    width: 80px;
    height: 80px;
    z-index: 5;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: radial-gradient(circle,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-logo-img {
    width: 60px;
    height: 60px;
    object-fit: contain;
    filter: drop-shadow(0 0 10px rgba(0, 255, 209, 0.3));
    animation: logoFloat 4s ease-in-out infinite;
}

/* Logo Energy Particles */
.logo-energy-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 1) 0%,
            rgba(0, 163, 255, 0.8) 50%,
            transparent 100%);
    border-radius: 50%;
    animation: particleOrbit 8s linear infinite;
}

.particle-1 {
    top: 10%;
    left: 50%;
    animation-delay: 0s;
    animation-duration: 6s;
}

.particle-2 {
    top: 25%;
    right: 15%;
    animation-delay: -1s;
    animation-duration: 7s;
}

.particle-3 {
    bottom: 25%;
    right: 10%;
    animation-delay: -2s;
    animation-duration: 8s;
}

.particle-4 {
    bottom: 10%;
    left: 50%;
    animation-delay: -3s;
    animation-duration: 6s;
}

.particle-5 {
    bottom: 25%;
    left: 15%;
    animation-delay: -4s;
    animation-duration: 7s;
}

.particle-6 {
    top: 25%;
    left: 10%;
    animation-delay: -5s;
    animation-duration: 8s;
}

.particle-7 {
    top: 50%;
    right: 5%;
    animation-delay: -6s;
    animation-duration: 9s;
}

.particle-8 {
    top: 50%;
    left: 5%;
    animation-delay: -7s;
    animation-duration: 9s;
}

/* Logo Animations */
@keyframes logoGlowPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes logoFloat {

    0%,
    100% {
        transform: translateY(0px) scale(1);
    }

    50% {
        transform: translateY(-3px) scale(1.02);
    }
}

@keyframes particleOrbit {
    0% {
        transform: rotate(0deg) translateX(40px) rotate(0deg);
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        transform: rotate(360deg) translateX(40px) rotate(-360deg);
        opacity: 0;
    }
}

/* ===================================
   MOBILE RESPONSIVE DESIGN
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .energy-field-system {
        width: 500px;
        height: 500px;
    }

    .ring-outer-1 {
        width: 480px;
        height: 480px;
    }

    .ring-outer-2 {
        width: 430px;
        height: 430px;
    }

    .ring-outer-3 {
        width: 380px;
        height: 380px;
    }

    .ring-middle-1 {
        width: 320px;
        height: 320px;
    }

    .ring-middle-2 {
        width: 270px;
        height: 270px;
    }

    .ring-inner-1 {
        width: 220px;
        height: 220px;
    }

    .ring-inner-2 {
        width: 170px;
        height: 170px;
    }

    .ring-core {
        width: 120px;
        height: 120px;
    }

    .logo-center {
        width: 100px;
        height: 100px;
    }

    .logo-image-container {
        width: 70px;
        height: 70px;
    }

    .hero-logo-img {
        width: 50px;
        height: 50px;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .hero-section {
        padding: 80px 0 60px;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .hero-container {
        padding: 0 20px;
        width: 100%;
    }

    .hero-layout {
        flex-direction: column;
        gap: 30px;
        text-align: center;
        align-items: center;
        justify-content: center;
        min-height: calc(100vh - 140px);
    }

    .hero-content-section {
        order: 2;
        max-width: 100%;
        width: 100%;
    }

    .hero-logo-section {
        order: 1;
        max-width: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
    }

    .energy-field-system {
        width: 250px;
        height: 250px;
        position: relative;
        margin: 0 auto;
    }

    /* Slower animations for mobile */
    .ring-outer-1 {
        width: 240px;
        height: 240px;
        animation: ringRotateClockwise 60s linear infinite;
    }

    .ring-outer-2 {
        width: 210px;
        height: 210px;
        animation: ringRotateCounterClockwise 55s linear infinite;
    }

    .ring-outer-3 {
        width: 180px;
        height: 180px;
        animation: ringRotateClockwise 50s linear infinite;
    }

    .ring-middle-1 {
        width: 150px;
        height: 150px;
        animation: ringRotateCounterClockwise 45s linear infinite;
    }

    .ring-middle-2 {
        width: 120px;
        height: 120px;
        animation: ringRotateClockwise 40s linear infinite;
    }

    .ring-inner-1 {
        width: 90px;
        height: 90px;
        animation: ringRotateCounterClockwise 35s linear infinite;
    }

    .ring-inner-2 {
        width: 70px;
        height: 70px;
        animation: ringRotateClockwise 30s linear infinite;
    }

    .ring-core {
        width: 50px;
        height: 50px;
        animation: ringRotateCounterClockwise 25s linear infinite;
    }

    .logo-center {
        width: 45px;
        height: 45px;
    }

    .logo-image-container {
        width: 35px;
        height: 35px;
    }

    .hero-logo-img {
        width: 25px;
        height: 25px;
    }

    /* Slower particle animations */
    .particle {
        animation-duration: 15s !important;
    }

    .logo-glow-effect {
        animation: logoGlowPulse 7s ease-in-out infinite alternate;
    }

    .hero-logo-img {
        animation: logoFloat 8s ease-in-out infinite;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .energy-field-system {
        width: 280px;
        height: 280px;
    }

    /* Even slower animations for small mobile */
    .ring-outer-1 {
        width: 270px;
        height: 270px;
        animation: ringRotateClockwise 60s linear infinite;
    }

    .ring-outer-2 {
        width: 240px;
        height: 240px;
        animation: ringRotateCounterClockwise 55s linear infinite;
    }

    .ring-outer-3 {
        width: 210px;
        height: 210px;
        animation: ringRotateClockwise 50s linear infinite;
    }

    .ring-middle-1 {
        width: 180px;
        height: 180px;
        animation: ringRotateCounterClockwise 45s linear infinite;
    }

    .ring-middle-2 {
        width: 150px;
        height: 150px;
        animation: ringRotateClockwise 40s linear infinite;
    }

    .ring-inner-1 {
        width: 120px;
        height: 120px;
        animation: ringRotateCounterClockwise 35s linear infinite;
    }

    .ring-inner-2 {
        width: 90px;
        height: 90px;
        animation: ringRotateClockwise 30s linear infinite;
    }

    .ring-core {
        width: 60px;
        height: 60px;
        animation: ringRotateCounterClockwise 25s linear infinite;
    }

    .logo-center {
        width: 50px;
        height: 50px;
    }

    .logo-image-container {
        width: 40px;
        height: 40px;
    }

    .hero-logo-img {
        width: 28px;
        height: 28px;
    }

    /* Much slower animations */
    .particle {
        animation-duration: 15s !important;
    }

    .logo-glow-effect {
        animation: logoGlowPulse 7s ease-in-out infinite alternate;
    }

    .hero-logo-img {
        animation: logoFloat 8s ease-in-out infinite;
    }
}

/* RTL Support */
.rtl .hero-layout {
    direction: rtl;
}

.rtl .hero-content-section {
    text-align: right;
}

.rtl .hero-actions {
    flex-direction: row-reverse;
}

.rtl .hero-stats {
    flex-direction: row-reverse;
}

.rtl .cta-primary .btn-icon svg {
    transform: scaleX(-1);
}

.rtl .scroll-indicator {
    text-align: center;
}

@media (max-width: 767px) {
    .rtl .hero-content-section {
        text-align: center;
    }

    .rtl .hero-actions {
        flex-direction: column;
    }

    .rtl .hero-stats {
        flex-direction: column;
    }
}

/* High Performance Mode - Reduce animations */
@media (prefers-reduced-motion: reduce) {

    .energy-ring,
    .particle,
    .logo-glow-effect,
    .hero-logo-img {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .energy-ring {
        border-color: var(--primary);
        background: rgba(0, 163, 255, 0.3);
    }

    .hero-logo-img {
        filter: contrast(1.5) brightness(1.2);
    }
}