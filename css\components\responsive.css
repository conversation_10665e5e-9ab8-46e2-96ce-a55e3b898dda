/* Responsive Design - Complete Implementation */

/* Large Tablets (1024px and down) */
@media (max-width: 1024px) {
    /* Header Styles */
    .header-container {
        padding: 1rem 2rem;
    }

    .logo-type {
        font-size: 1.2rem;
    }

    .nav-list {
        gap: 1.5rem;
    }

    /* Hero Section */
    .hero-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .hero-text-column {
        order: 2;
        text-align: center;
    }

    .hero-logo-column {
        order: 1;
    }

    .hero-content-wrapper {
        max-width: 800px;
        margin: 0 auto;
    }

    .circular-features {
        justify-content: center;
        gap: 2rem;
    }

    /* About Section */
    .about-content {
        padding: 4rem 2rem;
    }

    /* Rebranding Section */
    .rebranding-container {
        padding: 4rem 2rem;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

/* Medium Tablets (768px and down) */
@media (max-width: 768px) {
    /* Header Styles */
    .main-header {
        padding: 0.5rem;
    }

    .nav-list {
        display: none;
    }

    .menu-toggle {
        display: block;
    }

    .header-actions {
        gap: 1rem;
    }

    .action-btn.cta-button {
        display: none;
    }

    /* Hero Section */
    .hero-section {
        padding: 6rem 1rem 4rem;
    }

    .hero-content-wrapper {
        padding: 2rem 1.5rem;
    }

    .section-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .main-description {
        font-size: 1rem;
        line-height: 1.6;
    }

    .secondary-description {
        font-size: 0.9rem;
    }

    .circular-features {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .feature-circle {
        width: 280px;
        height: 280px;
    }

    .feature-title {
        font-size: 1.2rem;
    }

    .feature-desc {
        font-size: 0.9rem;
    }

    /* Logo Animation */
    .logo-animation-container {
        width: 300px;
        height: 300px;
    }

    .energy-ring {
        border-width: 2px;
    }

    .orbital-paths circle {
        stroke-width: 1px;
    }

    /* About Section */
    .about-content {
        padding: 3rem 1rem;
    }

    .reveal-card {
        padding: 1.5rem;
    }

    .cards-container {
        gap: 1.5rem;
    }

    /* Rebranding Section */
    .rebranding-container {
        padding: 3rem 1rem;
    }

    .section-subtitle {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .section-description {
        font-size: 0.9rem;
        max-width: 100%;
    }

    .timeline-container {
        padding: 2rem 0;
    }

    .timeline-item {
        padding-left: 3rem;
    }

    .timeline-content {
        width: 100%;
        text-align: left;
        padding: 1rem;
    }

    .timeline-content.left,
    .timeline-content.right {
        margin: 0;
    }

    .timeline-line {
        left: 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .stat-item {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-label {
        font-size: 1rem;
    }
}

/* Mobile Devices (480px and down) */
@media (max-width: 480px) {
    /* Header Styles */
    .header-container {
        padding: 0.5rem 1rem;
    }

    .logo-mark svg {
        width: 30px;
        height: 30px;
    }

    .logo-type {
        font-size: 1rem;
    }

    /* Hero Section */
    .hero-section {
        padding: 5rem 1rem 3rem;
    }

    .hero-content-wrapper {
        padding: 1.5rem 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .main-description,
    .secondary-description {
        font-size: 0.9rem;
    }

    .feature-circle {
        width: 240px;
        height: 240px;
    }

    .feature-icon-wrapper {
        width: 50px;
        height: 50px;
    }

    /* Logo Animation */
    .logo-animation-container {
        width: 250px;
        height: 250px;
    }

    .energy-ring {
        border-width: 1px;
    }

    /* About Section */
    .reveal-card {
        padding: 1.25rem;
    }

    .reveal-card h3 {
        font-size: 1.2rem;
    }

    /* Rebranding Section */
    .rebranding-header {
        margin-bottom: 2rem;
    }

    .timeline-item {
        padding-left: 2.5rem;
    }

    .timeline-content {
        padding: 0.75rem;
    }

    .timeline-content h3 {
        font-size: 1.2rem;
    }

    .feature-card {
        padding: 1.25rem;
    }

    .feature-icon {
        margin-bottom: 1rem;
    }

    .stat-item {
        padding: 1.25rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }
}

/* Performance Optimizations */
@media (max-width: 768px) {
    .energy-effects,
    .floating-elements,
    .bg-particles {
        opacity: 0.5;
    }

    .energy-ring {
        animation-duration: 8s;
    }

    .orbital-paths circle {
        animation-duration: 15s;
    }
}

/* Touch Device Optimizations */
@media (hover: none) {
    .feature-card,
    .timeline-content,
    .stat-item {
        transition: none;
    }

    .nav-link:hover .nav-line {
        transform: none;
    }

    .action-btn:hover {
        transform: none;
    }
}

/* Landscape Mode */
@media (max-height: 600px) and (orientation: landscape) {
    .hero-section {
        min-height: auto;
        padding: 2rem 1rem;
    }

    .hero-grid {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .hero-text-column {
        order: 1;
    }

    .hero-logo-column {
        order: 2;
    }

    .logo-animation-container {
        width: 200px;
        height: 200px;
    }

    .circular-features {
        flex-direction: row;
        gap: 1rem;
    }

    .feature-circle {
        width: 200px;
        height: 200px;
    }
}

/* Print Media */
@media print {
    .hero-section,
    .about-section,
    .rebranding-section {
        page-break-inside: avoid;
    }

    .energy-effects,
    .floating-elements,
    .bg-particles,
    .energy-rings,
    .orbital-paths {
        display: none;
    }
}

/* High-DPI Screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .main-logo {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .hero-content-wrapper,
    .feature-card,
    .timeline-content {
        background: rgba(255, 255, 255, 0.05);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .energy-ring,
    .orbital-paths circle,
    .floating-elements,
    .bg-particles {
        animation: none;
    }

    .feature-card,
    .timeline-content,
    .stat-item {
        transition: none;
    }
}

