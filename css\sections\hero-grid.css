/* Hero Grid Layout System */
.hero-section {
    min-height: 100vh;
    padding: 6rem 2rem;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-content {
    width: 100%;
    max-width: 1440px; /* محدودیت حداکثر عرض برای صفحات بزرگ */
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.hero-grid {
    display: grid;
    grid-template-columns: minmax(600px, 1.2fr) minmax(400px, 0.8fr);
    gap: clamp(2rem, 4vw, 6rem);
    align-items: center;
    direction: rtl;
}

/* Hero Text Column */
.hero-text-column {
    min-width: 600px;
    max-width: 800px;
    padding: clamp(1.5rem, 3vw, 3rem);
}

.hero-content-wrapper {
    width: 100%;
    height: 100%;
    padding: clamp(2rem, 4vw, 3rem);
    border-radius: 24px;
    position: relative;
}

/* Hero Logo Column */
.hero-logo-column {
    min-width: 400px;
    max-width: 600px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-animation-container {
    width: clamp(400px, 50vw, 500px);
    height: clamp(400px, 50vw, 500px);
    margin: 0 auto;
}

/* Content Grid Inside Text Column */
.content-grid {
    display: grid;
    gap: clamp(1.5rem, 2vw, 2.5rem);
}

.content-grid.compact {
    max-width: 700px;
    margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 1400px) {
    .hero-grid {
        grid-template-columns: 1.1fr 0.9fr;
        gap: 4rem;
    }

    .hero-text-column {
        min-width: 500px;
        max-width: 700px;
    }

    .hero-logo-column {
        min-width: 350px;
        max-width: 500px;
    }
}

@media (max-width: 1200px) {
    .hero-grid {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
    }

    .hero-text-column {
        min-width: 450px;
        padding: 1.5rem;
    }

    .hero-logo-column {
        min-width: 300px;
    }

    .logo-animation-container {
        width: clamp(350px, 45vw, 450px);
        height: clamp(350px, 45vw, 450px);
    }
}

@media (max-width: 992px) {
    .hero-section {
        padding: 4rem 1.5rem;
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hero-text-column,
    .hero-logo-column {
        min-width: unset;
        max-width: 600px;
        width: 100%;
        margin: 0 auto;
    }

    .logo-animation-container {
        width: clamp(300px, 40vw, 400px);
        height: clamp(300px, 40vw, 400px);
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 1rem;
    }

    .hero-content-wrapper {
        padding: 1.5rem;
    }

    .logo-animation-container {
        width: clamp(250px, 35vw, 350px);
        height: clamp(250px, 35vw, 350px);
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: 2rem 0.8rem;
    }

    .hero-content-wrapper {
        padding: 1.2rem;
    }

    .logo-animation-container {
        width: clamp(200px, 30vw, 300px);
        height: clamp(200px, 30vw, 300px);
    }
}

/* Print Media Query */
@media print {
    .hero-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .hero-logo-column {
        display: none; /* Hide animations for print */
    }
}