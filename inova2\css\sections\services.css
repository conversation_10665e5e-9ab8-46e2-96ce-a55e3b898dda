/* Unified styles for the Services section and smooth transitions between sections */

body {
    margin: 0;
    font-family: 'Vazirmatn', sans-serif;
}

.section {
    padding: 60px 20px;
    transition: background-color 0.5s ease, transform 0.5s ease;
}

.section:nth-child(even) {
    background-color: #f9f9f9;
}

.section:nth-child(odd) {
    background-color: #ffffff;
}

.services {
    text-align: center;
}

.services h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #333;
}

.services p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 40px;
}

.service-card {
    display: inline-block;
    width: 300px;
    margin: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 15px;
}

.service-title {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.service-description {
    font-size: 1rem;
    color: #666;
}

/* Smooth scrolling effect */
html {
    scroll-behavior: smooth;
}

/* Add animations for section transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.section {
    animation: fadeIn 0.8s ease forwards;
}