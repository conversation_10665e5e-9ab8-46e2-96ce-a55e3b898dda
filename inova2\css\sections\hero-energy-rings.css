/* ===================================
   HERO ENERGY RINGS - PROFESSIONAL DESIGN
   Advanced Energy Field Animation System
   =================================== */

/* Logo Section - Professional Energy Design */
.hero-logo-section {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    min-height: 600px;
    padding: 40px 20px;
}

.logo-container {
    position: relative;
    width: 500px;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Professional Energy Field System */
.energy-field-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.energy-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    opacity: 0.8;
}

/* Outer Energy Rings - Slow Rotation */
.ring-outer-1 {
    width: 480px;
    height: 480px;
    border: 1px solid rgba(0, 163, 255, 0.2);
    animation: energyRotateOuter1 30s linear infinite;
    box-shadow: 0 0 20px rgba(0, 163, 255, 0.1);
}

.ring-outer-2 {
    width: 420px;
    height: 420px;
    border: 1px solid rgba(0, 255, 209, 0.25);
    animation: energyRotateOuter2 25s linear infinite reverse;
    box-shadow: 0 0 15px rgba(0, 255, 209, 0.1);
}

.ring-outer-3 {
    width: 360px;
    height: 360px;
    border: 1px solid rgba(0, 163, 255, 0.3);
    animation: energyRotateOuter3 22s linear infinite;
    box-shadow: 0 0 12px rgba(0, 163, 255, 0.15);
}

/* Middle Energy Rings - Medium Speed */
.ring-middle-1 {
    width: 300px;
    height: 300px;
    border: 2px solid rgba(0, 255, 209, 0.4);
    animation: energyRotateMiddle1 18s linear infinite reverse;
    box-shadow: 0 0 10px rgba(0, 255, 209, 0.2);
}

.ring-middle-2 {
    width: 240px;
    height: 240px;
    border: 2px solid rgba(0, 163, 255, 0.45);
    animation: energyRotateMiddle2 15s linear infinite;
    box-shadow: 0 0 8px rgba(0, 163, 255, 0.25);
}

/* Inner Energy Rings - Fast Rotation */
.ring-inner-1 {
    width: 180px;
    height: 180px;
    border: 2px solid rgba(0, 255, 209, 0.6);
    animation: energyRotateInner1 12s linear infinite reverse;
    box-shadow: 0 0 6px rgba(0, 255, 209, 0.3);
}

.ring-inner-2 {
    width: 120px;
    height: 120px;
    border: 2px solid rgba(0, 163, 255, 0.7);
    animation: energyRotateInner2 10s linear infinite;
    box-shadow: 0 0 4px rgba(0, 163, 255, 0.35);
}

/* Core Energy Ring - Pulsing */
.ring-core {
    width: 80px;
    height: 80px;
    border: 3px solid rgba(0, 255, 209, 0.8);
    animation: coreEnergyPulse 8s ease-in-out infinite alternate;
    box-shadow:
        0 0 20px rgba(0, 255, 209, 0.5),
        inset 0 0 20px rgba(0, 255, 209, 0.3);
}

/* Ring Effects */
.ring-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg,
            rgba(0, 163, 255, 0.1),
            rgba(0, 255, 209, 0.1));
    animation: glowPulse 4s ease-in-out infinite alternate;
}

.ring-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.ring-particles::before,
.ring-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(0, 255, 209, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 6px rgba(0, 255, 209, 0.6);
}

.ring-particles::before {
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    animation: particleOrbit1 6s linear infinite;
}

.ring-particles::after {
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    animation: particleOrbit2 6s linear infinite reverse;
}

.ring-pulse {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 50%;
    border: 1px solid rgba(0, 163, 255, 0.3);
    animation: pulseBeat 3s ease-in-out infinite;
}

.ring-energy-flow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(from 0deg,
            transparent 0deg,
            rgba(0, 255, 209, 0.2) 90deg,
            transparent 180deg,
            rgba(0, 163, 255, 0.2) 270deg,
            transparent 360deg);
    animation: energyFlow 8s linear infinite;
}

.ring-spark {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.ring-spark::before {
    content: '';
    position: absolute;
    top: -3px;
    left: 50%;
    width: 6px;
    height: 6px;
    background: rgba(0, 255, 209, 1);
    border-radius: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 10px rgba(0, 255, 209, 0.8);
    animation: sparkRotate 4s linear infinite;
}

.ring-rotation {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 50%;
    border: 1px solid transparent;
    border-top-color: rgba(0, 163, 255, 0.5);
    animation: fastRotate 2s linear infinite;
}

.core-energy {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
            rgba(0, 255, 209, 0.3) 0%,
            rgba(0, 163, 255, 0.2) 50%,
            transparent 100%);
    border-radius: 50%;
    animation: coreGlow 6s ease-in-out infinite alternate;
}

.core-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    transform: translate(-50%, -50%);
    background: rgba(0, 255, 209, 0.4);
    border-radius: 50%;
    animation: corePulse 2s ease-in-out infinite;
}

/* Logo Center - Perfect Positioning */
.logo-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-glow-effect {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: radial-gradient(circle,
            rgba(0, 255, 209, 0.2) 0%,
            rgba(0, 163, 255, 0.1) 50%,
            transparent 100%);
    animation: logoGlow 4s ease-in-out infinite alternate;
}

.logo-shadow {
    position: absolute;
    top: 5px;
    left: 5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.2);
    filter: blur(10px);
}

.logo-image-container {
    position: relative;
    z-index: 2;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-logo-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 0 10px rgba(0, 255, 209, 0.3));
    animation: logoFloat 6s ease-in-out infinite alternate;
}

/* Logo Energy Particles */
.logo-energy-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: rgba(0, 255, 209, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 6px rgba(0, 255, 209, 0.6);
}

.particle-1 {
    top: 10%;
    left: 50%;
    animation: particleFloat1 8s ease-in-out infinite;
}

.particle-2 {
    top: 20%;
    right: 20%;
    animation: particleFloat2 7s ease-in-out infinite;
}

.particle-3 {
    top: 50%;
    right: 10%;
    animation: particleFloat3 9s ease-in-out infinite;
}

.particle-4 {
    bottom: 20%;
    right: 20%;
    animation: particleFloat4 6s ease-in-out infinite;
}

.particle-5 {
    bottom: 10%;
    left: 50%;
    animation: particleFloat5 8s ease-in-out infinite;
}

.particle-6 {
    bottom: 20%;
    left: 20%;
    animation: particleFloat6 7s ease-in-out infinite;
}

.particle-7 {
    top: 50%;
    left: 10%;
    animation: particleFloat7 9s ease-in-out infinite;
}

.particle-8 {
    top: 20%;
    left: 20%;
    animation: particleFloat8 6s ease-in-out infinite;
}

/* Energy Indicators */
.energy-indicators {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
}

.indicator {
    position: absolute;
    width: 8px;
    height: 8px;
}

.indicator-1 {
    top: 15%;
    left: 15%;
}

.indicator-2 {
    top: 15%;
    right: 15%;
}

.indicator-3 {
    bottom: 15%;
    right: 15%;
}

.indicator-4 {
    bottom: 15%;
    left: 15%;
}

.indicator-dot {
    width: 100%;
    height: 100%;
    background: rgba(0, 163, 255, 0.8);
    border-radius: 50%;
    animation: indicatorPulse 3s ease-in-out infinite;
}

.indicator-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(0, 163, 255, 0.4);
    border-radius: 50%;
    animation: waveExpand 4s ease-out infinite;
}

/* ===================================
   PROFESSIONAL ANIMATIONS
   =================================== */

/* Outer Ring Rotations - Slow & Smooth */
@keyframes energyRotateOuter1 {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes energyRotateOuter2 {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(-360deg);
    }
}

@keyframes energyRotateOuter3 {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Middle Ring Rotations - Medium Speed */
@keyframes energyRotateMiddle1 {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(-360deg);
    }
}

@keyframes energyRotateMiddle2 {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Inner Ring Rotations - Fast */
@keyframes energyRotateInner1 {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(-360deg);
    }
}

@keyframes energyRotateInner2 {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Core Energy Pulse */
@keyframes coreEnergyPulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
        box-shadow: 0 0 20px rgba(0, 255, 209, 0.5);
    }

    100% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 1;
        box-shadow: 0 0 30px rgba(0, 255, 209, 0.8);
    }
}

/* Glow Effects */
@keyframes glowPulse {
    0% {
        opacity: 0.3;
    }

    100% {
        opacity: 0.7;
    }
}

@keyframes pulseBeat {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.6;
    }

    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes energyFlow {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes sparkRotate {
    0% {
        transform: translateX(-50%) rotate(0deg) translateY(-20px) rotate(0deg);
    }

    100% {
        transform: translateX(-50%) rotate(360deg) translateY(-20px) rotate(-360deg);
    }
}

@keyframes fastRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes coreGlow {
    0% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }

    100% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes corePulse {

    0%,
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.4;
    }

    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.8;
    }
}

/* Logo Animations */
@keyframes logoFloat {
    0% {
        transform: translateY(0px);
    }

    100% {
        transform: translateY(-5px);
    }
}

@keyframes logoGlow {
    0% {
        opacity: 0.3;
    }

    100% {
        opacity: 0.6;
    }
}

/* Particle Animations */
@keyframes particleFloat1 {

    0%,
    100% {
        transform: translate(0, 0);
        opacity: 0.6;
    }

    50% {
        transform: translate(10px, -15px);
        opacity: 1;
    }
}

@keyframes particleFloat2 {

    0%,
    100% {
        transform: translate(0, 0);
        opacity: 0.6;
    }

    50% {
        transform: translate(-8px, 12px);
        opacity: 1;
    }
}

@keyframes particleFloat3 {

    0%,
    100% {
        transform: translate(0, 0);
        opacity: 0.6;
    }

    50% {
        transform: translate(-12px, -8px);
        opacity: 1;
    }
}

@keyframes particleFloat4 {

    0%,
    100% {
        transform: translate(0, 0);
        opacity: 0.6;
    }

    50% {
        transform: translate(15px, 10px);
        opacity: 1;
    }
}

@keyframes particleFloat5 {

    0%,
    100% {
        transform: translate(0, 0);
        opacity: 0.6;
    }

    50% {
        transform: translate(-5px, 18px);
        opacity: 1;
    }
}

@keyframes particleFloat6 {

    0%,
    100% {
        transform: translate(0, 0);
        opacity: 0.6;
    }

    50% {
        transform: translate(12px, -10px);
        opacity: 1;
    }
}

@keyframes particleFloat7 {

    0%,
    100% {
        transform: translate(0, 0);
        opacity: 0.6;
    }

    50% {
        transform: translate(8px, 14px);
        opacity: 1;
    }
}

@keyframes particleFloat8 {

    0%,
    100% {
        transform: translate(0, 0);
        opacity: 0.6;
    }

    50% {
        transform: translate(-10px, -12px);
        opacity: 1;
    }
}

/* Particle Orbit */
@keyframes particleOrbit1 {
    0% {
        transform: translateX(-50%) rotate(0deg) translateY(-10px) rotate(0deg);
    }

    100% {
        transform: translateX(-50%) rotate(360deg) translateY(-10px) rotate(-360deg);
    }
}

@keyframes particleOrbit2 {
    0% {
        transform: translateX(-50%) rotate(0deg) translateY(10px) rotate(0deg);
    }

    100% {
        transform: translateX(-50%) rotate(-360deg) translateY(10px) rotate(360deg);
    }
}

/* Indicator Animations */
@keyframes indicatorPulse {

    0%,
    100% {
        opacity: 0.6;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes waveExpand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }

    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* ===================================
   RESPONSIVE DESIGN - MOBILE OPTIMIZED
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .hero-logo-section {
        min-height: 500px;
        padding: 30px 15px;
    }

    .logo-container {
        width: 400px;
        height: 400px;
    }

    /* Scale down rings for tablet */
    .ring-outer-1 {
        width: 380px;
        height: 380px;
    }

    .ring-outer-2 {
        width: 330px;
        height: 330px;
    }

    .ring-outer-3 {
        width: 280px;
        height: 280px;
    }

    .ring-middle-1 {
        width: 230px;
        height: 230px;
    }

    .ring-middle-2 {
        width: 180px;
        height: 180px;
    }

    .ring-inner-1 {
        width: 130px;
        height: 130px;
    }

    .ring-inner-2 {
        width: 90px;
        height: 90px;
    }

    .ring-core {
        width: 60px;
        height: 60px;
    }

    .logo-center {
        width: 100px;
        height: 100px;
    }

    .logo-image-container {
        width: 70px;
        height: 70px;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .hero-logo-section {
        min-height: 400px;
        padding: 20px 10px;
    }

    .logo-container {
        width: 300px;
        height: 300px;
    }

    /* Mobile optimized rings - slower animations */
    .ring-outer-1 {
        width: 280px;
        height: 280px;
        animation: energyRotateOuter1 40s linear infinite;
    }

    .ring-outer-2 {
        width: 240px;
        height: 240px;
        animation: energyRotateOuter2 35s linear infinite reverse;
    }

    .ring-outer-3 {
        width: 200px;
        height: 200px;
        animation: energyRotateOuter3 32s linear infinite;
    }

    .ring-middle-1 {
        width: 160px;
        height: 160px;
        animation: energyRotateMiddle1 28s linear infinite reverse;
    }

    .ring-middle-2 {
        width: 120px;
        height: 120px;
        animation: energyRotateMiddle2 25s linear infinite;
    }

    .ring-inner-1 {
        width: 80px;
        height: 80px;
        animation: energyRotateInner1 22s linear infinite reverse;
    }

    .ring-inner-2 {
        width: 50px;
        height: 50px;
        animation: energyRotateInner2 20s linear infinite;
    }

    .ring-core {
        width: 30px;
        height: 30px;
        animation: coreEnergyPulse 12s ease-in-out infinite alternate;
    }

    .logo-center {
        width: 80px;
        height: 80px;
    }

    .logo-image-container {
        width: 60px;
        height: 60px;
    }

    /* Reduce particle count on mobile */
    .particle-3,
    .particle-4,
    .particle-7,
    .particle-8 {
        display: none;
    }

    /* Smaller energy indicators */
    .indicator {
        width: 6px;
        height: 6px;
    }

    .indicator-wave {
        width: 15px;
        height: 15px;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .hero-logo-section {
        min-height: 350px;
        padding: 15px 5px;
    }

    .logo-container {
        width: 250px;
        height: 250px;
    }

    /* Extra small rings for small mobile */
    .ring-outer-1 {
        width: 230px;
        height: 230px;
    }

    .ring-outer-2 {
        width: 200px;
        height: 200px;
    }

    .ring-outer-3 {
        width: 170px;
        height: 170px;
    }

    .ring-middle-1 {
        width: 140px;
        height: 140px;
    }

    .ring-middle-2 {
        width: 110px;
        height: 110px;
    }

    .ring-inner-1 {
        width: 80px;
        height: 80px;
    }

    .ring-inner-2 {
        width: 50px;
        height: 50px;
    }

    .ring-core {
        width: 25px;
        height: 25px;
    }

    .logo-center {
        width: 70px;
        height: 70px;
    }

    .logo-image-container {
        width: 50px;
        height: 50px;
    }

    /* Hide more particles on very small screens */
    .particle-2,
    .particle-6 {
        display: none;
    }
}

/* RTL Support */
.rtl .energy-ring {
    animation-direction: reverse;
}

.rtl .ring-outer-2,
.rtl .ring-middle-1,
.rtl .ring-inner-1 {
    animation-direction: normal;
}

/* High Performance Mode - Reduce animations */
@media (prefers-reduced-motion: reduce) {

    .energy-ring,
    .particle,
    .indicator-dot,
    .indicator-wave,
    .hero-logo-img {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
    }

    .ring-glow,
    .ring-pulse,
    .ring-energy-flow,
    .ring-spark,
    .ring-rotation,
    .core-energy,
    .core-pulse,
    .logo-glow-effect {
        animation: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .energy-ring {
        border-color: var(--primary) !important;
        opacity: 1 !important;
    }

    .particle {
        background: var(--accent) !important;
        box-shadow: 0 0 10px var(--accent) !important;
    }

    .indicator-dot {
        background: var(--primary) !important;
    }

    .hero-logo-img {
        filter: drop-shadow(0 0 15px var(--accent)) !important;
    }
}