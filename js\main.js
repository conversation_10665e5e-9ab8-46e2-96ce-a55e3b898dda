jQuery(document).ready(function ($) {
    'use strict';

    // Performance check
    const isMobile = window.innerWidth <= 768;
    const isLowPerformance = navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2;

    // Initialize AOS with performance considerations
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: isMobile ? 400 : 800,
            once: true,
            offset: isMobile ? 50 : 100,
            disable: isLowPerformance ? 'mobile' : false
        });
    }

    // Throttled scroll handler for better performance
    let scrollTimeout;
    $(window).scroll(function () {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        scrollTimeout = setTimeout(function () {
            if ($(window).scrollTop() > 50) {
                $('.site-header').addClass('scrolled');
            } else {
                $('.site-header').removeClass('scrolled');
            }
        }, 10);
    });

    // Mobile menu toggle with performance optimization
    $('.mobile-menu-toggle').click(function () {
        $(this).toggleClass('active');
        $('.mobile-navigation').toggleClass('active');
        $('body').toggleClass('menu-open');
    });

    // Close mobile menu when clicking on a link
    $('.mobile-nav-menu a').click(function () {
        $('.mobile-menu-toggle').removeClass('active');
        $('.mobile-navigation').removeClass('active');
        $('body').removeClass('menu-open');
    });

    // Optimized smooth scrolling
    $('a[href^="#"]').on('click', function (e) {
        e.preventDefault();
        const target = $(this.getAttribute('href'));
        if (target.length) {
            const scrollTop = target.offset().top - 80;
            if (isMobile) {
                // Faster scrolling on mobile
                $('html, body').animate({
                    scrollTop: scrollTop
                }, 400);
            } else {
                $('html, body').animate({
                    scrollTop: scrollTop
                }, 800);
            }
        }
    });

    // Performance optimizations for mobile
    if (isMobile) {
        // Reduce animation complexity
        $('.energy-rings').addClass('reduced-motion');

        // Disable heavy animations on low-performance devices
        if (isLowPerformance) {
            $('*').css({
                'animation-duration': '0.1s',
                'transition-duration': '0.1s'
            });
        }

        // Lazy load heavy elements
        setTimeout(function () {
            $('.particle-field, .floating-particles').addClass('loaded');
        }, 1000);
    }

    // Intersection Observer for better performance
    if ('IntersectionObserver' in window) {
        const observerOptions = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        const observer = new IntersectionObserver(function (entries) {
            entries.forEach(function (entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('in-view');
                    // Stop observing once element is in view
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe sections for lazy loading
        document.querySelectorAll('section').forEach(function (section) {
            observer.observe(section);
        });
    }
});
