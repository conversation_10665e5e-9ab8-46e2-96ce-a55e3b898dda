<!DOCTYPE html>
<html <?php language_attributes(); ?> dir="<?php echo (get_current_language() === 'fa') ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<a class="skip-link screen-reader-text" href="#main"><?php echo inova_translate('skip_to_content', 'رفتن به محتوا', 'Skip to content'); ?></a>

<!-- Header -->
<header class="main-header">
    <div class="header-container">
        <!-- Logo -->
        <a href="<?php echo esc_url(home_url('/')); ?>" class="header-logo">
            <div class="logo-mark">
                <svg viewBox="0 0 50 50">
                    <defs>
                        <linearGradient id="logo-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:var(--primary)" />
                            <stop offset="100%" style="stop-color:var(--accent)" />
                        </linearGradient>
                    </defs>
                    <path class="logo-path" fill="url(#logo-gradient)" 
                          d="M25,5 L45,25 L25,45 L5,25 L25,5Z" />
                    <circle class="logo-orbit" cx="25" cy="25" r="20" 
                            stroke="rgba(255,255,255,0.1)" fill="none" />
                </svg>
            </div>
            <div class="logo-type">
                <span class="logo-text">INOVA</span>
                <span class="logo-tagline">Energy</span>
            </div>
        </a>

        <!-- Main Navigation -->
        <nav class="main-nav">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_class' => 'nav-list',
                'container' => false,
                'fallback_cb' => 'inova_default_menu',
            ));
            ?>
        </nav>

        <!-- Action Buttons -->
        <div class="header-actions">
            <!-- Language Switch Button -->
            <button class="action-btn lang-switch" aria-label="<?php echo inova_translate('change_language', 'تغییر زبان', 'Change Language'); ?>" onclick="toggleLanguage()">
                <svg viewBox="0 0 24 24" class="icon">
                    <path d="M12.87,15.07L10.33,12.56L10.36,12.53C12.1,10.59 13.34,8.36 14.07,6H17V4H10V2H8V4H1V6H12.17C11.5,7.92 10.44,9.75 9,11.35C8.07,10.32 7.3,9.19 6.69,8H4.69C5.42,9.63 6.42,11.17 7.67,12.56L2.58,17.58L4,19L9,14L12.11,17.11L12.87,15.07M18.5,10H16.5L12,22H14L15.12,19H19.87L21,22H23L18.5,10M15.88,17L17.5,12.67L19.12,17H15.88Z"/>
                </svg>
                <span class="lang-text"><?php echo inova_translate('lang_code', 'EN', 'فا'); ?></span>
            </button>

            <a href="#contact" class="action-btn cta-button">
                <span class="btn-text"><?php echo inova_translate('start_project', 'شروع پروژه', 'Start Project'); ?></span>
                <div class="btn-particles"></div>
                <svg class="btn-arrow" viewBox="0 0 24 24">
                    <?php if (get_current_language() === 'fa'): ?>
                        <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
                    <?php else: ?>
                        <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                    <?php endif; ?>
                </svg>
            </a>

            <button class="action-btn menu-toggle" aria-label="<?php echo inova_translate('menu', 'منو', 'Menu'); ?>">
                <span class="menu-line"></span>
                <span class="menu-line"></span>
                <span class="menu-line"></span>
            </button>
        </div>
    </div>
</header>

<?php
// Default menu fallback
function inova_default_menu() {
    $current_lang = get_current_language();
    
    if ($current_lang === 'fa') {
        $menu_items = array(
            array('url' => '#hero', 'text' => 'خانه', 'active' => true),
            array('url' => '#about', 'text' => 'درباره ما'),
            array('url' => '#services', 'text' => 'خدمات'),
            array('url' => '#projects', 'text' => 'پروژه‌ها'),
            array('url' => '#contact', 'text' => 'تماس'),
        );
    } else {
        $menu_items = array(
            array('url' => '#hero', 'text' => 'Home', 'active' => true),
            array('url' => '#about', 'text' => 'About'),
            array('url' => '#services', 'text' => 'Services'),
            array('url' => '#projects', 'text' => 'Projects'),
            array('url' => '#contact', 'text' => 'Contact'),
        );
    }
    
    echo '<ul class="nav-list">';
    foreach ($menu_items as $item) {
        $active_class = isset($item['active']) ? ' active' : '';
        echo '<li class="nav-item">';
        echo '<a href="' . esc_url($item['url']) . '" class="nav-link' . $active_class . '">';
        echo '<span class="nav-text">' . esc_html($item['text']) . '</span>';
        echo '<span class="nav-line"></span>';
        echo '</a>';
        echo '</li>';
    }
    echo '</ul>';
}
?>
