/* ===================================
   INOVA ENERGY ACTIVITIES SECTION - FIXED
   Professional & Organized Design
   =================================== */

/* Activities Section Base */
.activities-section-fixed {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 1) 0%,
            rgba(0, 31, 63, 0.98) 50%,
            rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* Background */
.activities-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(0, 163, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 255, 209, 0.06) 0%, transparent 50%);
    animation: gradientPulse 18s ease-in-out infinite alternate;
}

@keyframes gradientPulse {
    0% {
        opacity: 0.6;
        transform: scale(1);
    }

    100% {
        opacity: 1;
        transform: scale(1.02);
    }
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 40px 60px, rgba(0, 163, 255, 0.15), transparent),
        radial-gradient(1px 1px at 80px 120px, rgba(0, 255, 209, 0.1), transparent);
    background-size: 120px 120px;
    animation: patternDrift 30s linear infinite;
    opacity: 0.3;
}

@keyframes patternDrift {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-120px, -120px);
    }
}

.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape-1,
.shape-2 {
    position: absolute;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.04),
            rgba(0, 255, 209, 0.02));
    border-radius: 50%;
    backdrop-filter: blur(20px);
    animation: shapeFloat 20s ease-in-out infinite;
}

.shape-1 {
    width: 180px;
    height: 180px;
    top: 20%;
    left: -3%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 65%;
    right: -2%;
    animation-delay: -10s;
}

@keyframes shapeFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.3;
    }

    50% {
        transform: translateY(-40px) rotate(180deg);
        opacity: 0.6;
    }
}

/* Activities Container */
.activities-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.activities-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 24px;
    font-size: 0.9rem;
    color: var(--accent);
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.1rem;
}

.section-title {
    margin-bottom: 24px;
}

.title-main {
    display: block;
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-light);
    line-height: 1.1;
    margin-bottom: 8px;
}

.title-sub {
    display: block;
    font-size: 1.8rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.section-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
}

/* Activities Grid */
.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    margin-bottom: 80px;
}

/* Activity Item */
.activity-item {
    position: relative;
}

.activity-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.activity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 163, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.activity-card:hover::before {
    left: 100%;
}

.activity-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.15);
}

/* Card Header */
.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}

.activity-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.activity-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.activity-card:hover .activity-icon::before {
    left: 100%;
}

.activity-card:hover .activity-icon {
    transform: scale(1.1) rotate(5deg);
}

.activity-icon svg {
    width: 30px;
    height: 30px;
    color: white;
    stroke-width: 2;
    position: relative;
    z-index: 2;
}

.activity-number {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 0.7;
}

/* Card Content */
.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.activity-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 16px;
    line-height: 1.3;
}

.activity-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 24px;
    flex: 1;
}

/* Activity Features */
.activity-features {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
}

.feature-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.feature-text {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Activity Stats */
.activity-stats {
    display: flex;
    gap: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Activities CTA */
.activities-cta {
    text-align: center;
    padding: 60px 40px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 25px;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.activities-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center,
            rgba(0, 163, 255, 0.1) 0%,
            transparent 70%);
    animation: ctaPulse 4s ease-in-out infinite alternate;
}

@keyframes ctaPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    text-decoration: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 163, 255, 0.4);
    gap: 16px;
}

.cta-button svg {
    width: 18px;
    height: 18px;
    stroke-width: 2;
    transition: transform 0.3s ease;
}

.cta-button:hover svg {
    transform: translateX(4px);
}

/* ===================================
   RESPONSIVE DESIGN - MOBILE OPTIMIZED
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .activities-container {
        padding: 0 30px;
    }

    .activities-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .activity-card {
        padding: 25px;
    }

    .activity-icon {
        width: 55px;
        height: 55px;
    }

    .activity-icon svg {
        width: 28px;
        height: 28px;
    }

    .activity-number {
        font-size: 1.8rem;
    }

    .activity-title {
        font-size: 1.3rem;
    }

    .activities-cta {
        padding: 50px 30px;
    }

    .cta-title {
        font-size: 1.8rem;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .activities-section-fixed {
        padding: 60px 0;
    }

    .activities-container {
        padding: 0 20px;
    }

    .activities-header {
        margin-bottom: 50px;
    }

    .header-badge {
        padding: 6px 16px;
        font-size: 0.85rem;
    }

    .title-main {
        font-size: 2rem;
        line-height: 1.2;
    }

    .title-sub {
        font-size: 1.3rem;
    }

    .section-description {
        font-size: 1rem;
        max-width: 100%;
    }

    .activities-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 50px;
    }

    .activity-card {
        padding: 20px;
        border-radius: 15px;
    }

    .card-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 16px;
        margin-bottom: 20px;
    }

    .activity-icon {
        width: 70px;
        height: 70px;
        order: 2;
    }

    .activity-icon svg {
        width: 35px;
        height: 35px;
    }

    .activity-number {
        font-size: 2.5rem;
        order: 1;
        margin-bottom: 8px;
    }

    .activity-title {
        font-size: 1.2rem;
        text-align: center;
        margin-bottom: 12px;
    }

    .activity-description {
        font-size: 0.95rem;
        text-align: center;
        margin-bottom: 20px;
    }

    .activity-features {
        gap: 10px;
        margin-bottom: 20px;
    }

    .feature-item {
        justify-content: center;
        padding: 6px 10px;
    }

    .feature-item:hover {
        transform: translateY(-2px);
    }

    .feature-text {
        font-size: 0.85rem;
    }

    .activity-stats {
        gap: 16px;
        padding-top: 16px;
        justify-content: center;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .activities-cta {
        padding: 40px 20px;
        border-radius: 20px;
    }

    .cta-title {
        font-size: 1.5rem;
        margin-bottom: 12px;
    }

    .cta-description {
        font-size: 1rem;
        margin-bottom: 24px;
    }

    .cta-button {
        padding: 14px 28px;
        font-size: 0.95rem;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .activities-section-fixed {
        padding: 50px 0;
    }

    .activities-container {
        padding: 0 15px;
    }

    .activities-header {
        margin-bottom: 40px;
    }

    .title-main {
        font-size: 1.8rem;
    }

    .title-sub {
        font-size: 1.1rem;
    }

    .section-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .activities-grid {
        gap: 16px;
        margin-bottom: 40px;
    }

    .activity-card {
        padding: 16px;
    }

    .activity-icon {
        width: 60px;
        height: 60px;
    }

    .activity-icon svg {
        width: 30px;
        height: 30px;
    }

    .activity-number {
        font-size: 2rem;
    }

    .activity-title {
        font-size: 1.1rem;
    }

    .activity-description {
        font-size: 0.9rem;
    }

    .feature-text {
        font-size: 0.8rem;
    }

    .activity-stats {
        gap: 12px;
    }

    .stat-number {
        font-size: 1.2rem;
    }

    .activities-cta {
        padding: 30px 16px;
    }

    .cta-title {
        font-size: 1.3rem;
    }

    .cta-description {
        font-size: 0.95rem;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}

/* RTL Support */
.rtl .activities-grid {
    direction: rtl;
}

.rtl .card-header {
    flex-direction: row-reverse;
}

.rtl .feature-item {
    flex-direction: row-reverse;
}

.rtl .feature-item:hover {
    transform: translateX(-5px);
}

.rtl .cta-button {
    flex-direction: row-reverse;
}

.rtl .cta-button:hover svg {
    transform: translateX(-4px);
}

@media (max-width: 767px) {
    .rtl .card-header {
        flex-direction: column;
    }

    .rtl .feature-item {
        flex-direction: row;
        justify-content: center;
    }

    .rtl .feature-item:hover {
        transform: translateY(-2px);
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .activities-section-fixed *,
    .activities-section-fixed *::before,
    .activities-section-fixed *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .activities-section-fixed {
        background: #000;
    }

    .activity-card,
    .activities-cta {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .activity-description,
    .feature-text,
    .cta-description {
        color: #fff;
    }
}