# INOVA Energy WordPress Theme

A modern, multilingual WordPress theme for INOVA Energy with Persian/English language support and RTL/LTR layouts.

## Features

### 🌐 Multilingual Support
- **Persian (RTL)**: Full right-to-left layout with Vazirmatn font
- **English (LTR)**: Left-to-right layout with Inter font
- **Dynamic Language Switching**: Seamless switching between languages with AJAX
- **Automatic Direction Change**: RTL/LTR layout changes automatically

### 🎨 Modern Design
- **Glass Morphism Effects**: Modern glassmorphism design elements
- **Gradient Animations**: Beautiful gradient backgrounds and animations
- **Particle Effects**: Interactive particle systems using Three.js
- **Responsive Design**: Fully responsive across all devices
- **Dark Theme**: Professional dark theme with energy-focused color scheme

### ⚡ Performance Optimized
- **Optimized Assets**: Minified CSS and JavaScript
- **Lazy Loading**: Images and content load on demand
- **Modern Libraries**: GSAP, AOS, Three.js for smooth animations
- **Clean Code**: Well-structured and documented code

### 🔧 WordPress Integration
- **Custom Post Types**: Ready for energy projects and services
- **Widget Areas**: Multiple widget-ready areas
- **Menu Support**: Custom navigation menus
- **SEO Friendly**: Optimized for search engines
- **Accessibility**: WCAG compliant design

## Installation

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- Modern web browser with JavaScript enabled

### Steps
1. **Download** the theme files
2. **Upload** to your WordPress themes directory (`/wp-content/themes/`)
3. **Activate** the theme from WordPress admin
4. **Configure** language settings if needed

## File Structure

```
inova-energy-theme/
├── style.css                 # Main theme stylesheet
├── index.php                 # Main template file
├── functions.php             # Theme functions
├── header.php                # Header template
├── footer.php                # Footer template
├── template-parts/           # Template parts
│   ├── hero.php
│   ├── about.php
│   ├── services.php
│   ├── projects.php
│   ├── activities.php
│   ├── rebranding.php
│   └── contact.php
├── css/                      # Stylesheets
│   ├── base/
│   │   └── styles.css
│   ├── components/
│   │   ├── header.css
│   │   ├── animations.css
│   │   ├── responsive.css
│   │   ├── multilingual.css
│   │   ├── english-layout.css
│   │   └── feature-circles.css
│   └── sections/
│       ├── hero.css
│       ├── about.css
│       ├── services.css
│       ├── projects.css
│       ├── activities.css
│       └── rebranding.css
├── js/                       # JavaScript files
│   ├── language-switcher.js
│   ├── main.js
│   ├── particles.js
│   ├── hero-animations.js
│   ├── header-animations.js
│   ├── content-animations.js
│   ├── feature-circles.js
│   ├── section-transitions.js
│   ├── rebranding.js
│   ├── globe.js
│   ├── stats.js
│   └── preloader.js
└── README.md
```

## Language Switching

The theme includes a sophisticated language switching system:

### How it Works
1. **Cookie-based**: Language preference stored in browser cookies
2. **AJAX Switching**: No page reload required
3. **Dynamic Content**: All content updates automatically
4. **Direction Change**: Layout direction changes (RTL/LTR)
5. **Font Switching**: Appropriate fonts load for each language

### Adding Translations
Use the `inova_translate()` function in your templates:

```php
<?php echo inova_translate('key', 'Persian Text', 'English Text'); ?>
```

### JavaScript Language Switching
```javascript
// Switch to English
toggleLanguage();

// Listen for language changes
$(document).on('languageChanged', function(event, newLang) {
    console.log('Language changed to:', newLang);
});
```

## Customization

### Colors
Edit the CSS variables in `css/base/styles.css`:

```css
:root {
    --primary: #00A3FF;
    --accent: #00FFD1;
    --secondary: #001F3F;
    --text-light: #FFFFFF;
}
```

### Fonts
- **Persian**: Vazirmatn (Google Fonts)
- **English**: Inter (Google Fonts)

### Animations
The theme uses several animation libraries:
- **AOS**: Scroll animations
- **GSAP**: Advanced animations
- **Three.js**: 3D effects and particles
- **Anime.js**: Lightweight animations

## Browser Support

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ Mobile browsers

## Performance

### Optimization Features
- Minified assets
- Optimized images
- Lazy loading
- Efficient animations
- Clean HTML structure

### Loading Speed
- First Contentful Paint: < 1.5s
- Largest Contentful Paint: < 2.5s
- Cumulative Layout Shift: < 0.1

## Accessibility

- WCAG 2.1 AA compliant
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- Focus indicators
- Semantic HTML structure

## SEO Features

- Semantic HTML5 structure
- Meta tags optimization
- Open Graph support
- Schema.org markup ready
- Clean URL structure
- Sitemap friendly

## Support

For support and customization:
- **Documentation**: Check this README
- **Issues**: Report bugs via GitHub issues
- **Customization**: Contact the development team

## Changelog

### Version 1.0
- Initial release
- Multilingual support (Persian/English)
- Modern design with animations
- Responsive layout
- WordPress integration
- Performance optimization

## License

This theme is proprietary software developed for INOVA Energy.

## Credits

### Libraries Used
- **WordPress**: Content management system
- **GSAP**: Animation library
- **Three.js**: 3D graphics library
- **AOS**: Animate On Scroll library
- **Anime.js**: Animation library
- **Lord Icons**: Icon library
- **Google Fonts**: Vazirmatn and Inter fonts

### Development Team
- **Theme Development**: INOVA Energy Development Team
- **Design**: Modern energy-focused design
- **Multilingual System**: Custom implementation
- **Performance Optimization**: Advanced optimization techniques

---

**INOVA Energy** - Leading the way in sustainable energy and innovative technologies.
