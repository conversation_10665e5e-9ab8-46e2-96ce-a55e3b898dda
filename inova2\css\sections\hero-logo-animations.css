/* Hero Logo Animations */

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }

}

@keyframes scaleUp {
    0% {
        transform: scale(0.8);
    }
    100% {
        transform: scale(1);
    }
}

.hero-logo {
    opacity: 0;
    animation: fadeIn 1s forwards, scaleUp 0.5s forwards;
}

.hero-logo.visible {
    opacity: 1;
}

.rebranding-logo {
    opacity: 0;
    animation: fadeIn 1s forwards;
}

.rebranding-logo.visible {
    opacity: 1;
}

.activities-logo {
    opacity: 0;
    animation: fadeIn 1s forwards;
}

.activities-logo.visible {
    opacity: 1;
}

.about-logo {
    opacity: 0;
    animation: fadeIn 1s forwards;
}

.about-logo.visible {
    opacity: 1;
}

/* Smooth transitions between sections */
section {
    transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

section.hidden {
    opacity: 0;
    transform: translateY(20px);
}

section.visible {
    opacity: 1;
    transform: translateY(0);
}