/* ===================================
   INOVA ENERGY RESPONSIVE MASTER
   Professional Mobile & Tablet Optimization
   =================================== */

/* CSS Variables for Responsive */
:root {
    /* Breakpoints */
    --mobile-small: 480px;
    --mobile: 768px;
    --tablet: 1024px;
    --desktop: 1200px;
    --desktop-large: 1400px;
    
    /* Spacing Scale */
    --space-xs: 8px;
    --space-sm: 16px;
    --space-md: 24px;
    --space-lg: 32px;
    --space-xl: 48px;
    --space-2xl: 64px;
    --space-3xl: 80px;
    
    /* Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
}

/* Base Responsive Utilities */
.container-responsive {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-md);
}

/* Responsive Grid System */
.grid-responsive {
    display: grid;
    gap: var(--space-lg);
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Typography */
.text-responsive-xl {
    font-size: clamp(2rem, 5vw, 3.5rem);
    line-height: 1.2;
}

.text-responsive-lg {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    line-height: 1.3;
}

.text-responsive-md {
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    line-height: 1.4;
}

.text-responsive-sm {
    font-size: clamp(1rem, 2.5vw, 1.3rem);
    line-height: 1.5;
}

/* Responsive Spacing */
.section-padding {
    padding: clamp(50px, 8vw, 100px) 0;
}

.section-margin {
    margin: clamp(40px, 6vw, 80px) 0;
}

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
    .container-responsive {
        padding: 0 var(--space-xl);
    }
    
    .grid-responsive {
        gap: var(--space-2xl);
    }
    
    /* Enhanced spacing for large screens */
    .section-padding {
        padding: 120px 0;
    }
    
    .section-margin {
        margin: 100px 0;
    }
    
    /* Typography enhancements */
    .text-responsive-xl {
        font-size: 3.5rem;
    }
    
    .text-responsive-lg {
        font-size: 2.8rem;
    }
    
    .text-responsive-md {
        font-size: 2rem;
    }
}

/* Desktop (1200px - 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .container-responsive {
        max-width: 1200px;
        padding: 0 var(--space-lg);
    }
    
    .grid-responsive {
        gap: var(--space-xl);
    }
    
    .section-padding {
        padding: 100px 0;
    }
    
    .section-margin {
        margin: 80px 0;
    }
}

/* Tablet Landscape (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
    .container-responsive {
        max-width: 1024px;
        padding: 0 var(--space-lg);
    }
    
    .grid-responsive {
        gap: var(--space-lg);
    }
    
    .grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .grid-3 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-padding {
        padding: 80px 0;
    }
    
    .section-margin {
        margin: 60px 0;
    }
    
    /* Tablet-specific adjustments */
    .text-responsive-xl {
        font-size: 2.5rem;
    }
    
    .text-responsive-lg {
        font-size: 2.2rem;
    }
    
    .text-responsive-md {
        font-size: 1.6rem;
    }
    
    /* Touch-friendly elements */
    button, .btn, .action-btn {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 24px;
    }
    
    /* Improved tap targets */
    .nav-link, .social-link, .contact-item {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
}

/* Tablet Portrait (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .container-responsive {
        max-width: 768px;
        padding: 0 var(--space-md);
    }
    
    .grid-responsive {
        gap: var(--space-md);
    }
    
    .grid-4,
    .grid-3,
    .grid-2 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-padding {
        padding: 60px 0;
    }
    
    .section-margin {
        margin: 50px 0;
    }
    
    /* Typography adjustments */
    .text-responsive-xl {
        font-size: 2.2rem;
        line-height: 1.3;
    }
    
    .text-responsive-lg {
        font-size: 2rem;
        line-height: 1.3;
    }
    
    .text-responsive-md {
        font-size: 1.5rem;
        line-height: 1.4;
    }
    
    .text-responsive-sm {
        font-size: 1.1rem;
        line-height: 1.5;
    }
    
    /* Enhanced touch targets */
    button, .btn, .action-btn {
        min-height: 48px;
        padding: 14px 28px;
        font-size: 1rem;
    }
    
    /* Card adjustments */
    .card, .service-card, .project-card, .feature-circle {
        padding: 30px 20px;
        border-radius: 16px;
    }
    
    /* Navigation improvements */
    .nav-link {
        padding: 12px 16px;
        font-size: 1rem;
    }
    
    /* Form improvements */
    input, textarea, select {
        min-height: 48px;
        padding: 12px 16px;
        font-size: 1rem;
        border-radius: 12px;
    }
    
    textarea {
        min-height: 120px;
    }
}

/* Mobile Large (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
    .container-responsive {
        max-width: 100%;
        padding: 0 var(--space-md);
    }
    
    .grid-responsive {
        gap: var(--space-md);
    }
    
    .grid-4,
    .grid-3,
    .grid-2 {
        grid-template-columns: 1fr;
    }
    
    .section-padding {
        padding: 50px 0;
    }
    
    .section-margin {
        margin: 40px 0;
    }
    
    /* Mobile typography */
    .text-responsive-xl {
        font-size: 2rem;
        line-height: 1.3;
    }
    
    .text-responsive-lg {
        font-size: 1.8rem;
        line-height: 1.3;
    }
    
    .text-responsive-md {
        font-size: 1.3rem;
        line-height: 1.4;
    }
    
    .text-responsive-sm {
        font-size: 1rem;
        line-height: 1.5;
    }
    
    /* Mobile-optimized buttons */
    button, .btn, .action-btn {
        min-height: 50px;
        padding: 15px 30px;
        font-size: 1rem;
        border-radius: 25px;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
        display: block;
    }
    
    /* Mobile cards */
    .card, .service-card, .project-card, .feature-circle {
        padding: 25px 20px;
        border-radius: 16px;
        text-align: center;
    }
    
    /* Mobile navigation */
    .nav-link {
        padding: 16px 20px;
        font-size: 1.1rem;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    /* Mobile forms */
    input, textarea, select {
        min-height: 50px;
        padding: 15px 20px;
        font-size: 1rem;
        border-radius: 12px;
        width: 100%;
    }
    
    textarea {
        min-height: 140px;
    }
    
    /* Mobile-specific layouts */
    .hero-grid,
    .about-cards-grid,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
        text-align: center;
    }
    
    /* Mobile statistics */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-sm);
    }
    
    .stat-card {
        padding: 20px 15px;
        text-align: center;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .stat-label {
        font-size: 0.85rem;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .container-responsive {
        padding: 0 var(--space-sm);
    }
    
    .grid-responsive {
        gap: var(--space-sm);
    }
    
    .section-padding {
        padding: 40px 0;
    }
    
    .section-margin {
        margin: 30px 0;
    }
    
    /* Extra small mobile typography */
    .text-responsive-xl {
        font-size: 1.8rem;
        line-height: 1.3;
    }
    
    .text-responsive-lg {
        font-size: 1.6rem;
        line-height: 1.3;
    }
    
    .text-responsive-md {
        font-size: 1.2rem;
        line-height: 1.4;
    }
    
    .text-responsive-sm {
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    /* Compact buttons */
    button, .btn, .action-btn {
        min-height: 48px;
        padding: 14px 24px;
        font-size: 0.95rem;
        border-radius: 24px;
    }
    
    /* Compact cards */
    .card, .service-card, .project-card, .feature-circle {
        padding: 20px 16px;
        border-radius: 14px;
    }
    
    /* Compact forms */
    input, textarea, select {
        min-height: 48px;
        padding: 14px 16px;
        font-size: 0.95rem;
        border-radius: 10px;
    }
    
    textarea {
        min-height: 120px;
    }
    
    /* Extra compact statistics */
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-xs);
    }
    
    .stat-card {
        padding: 16px 12px;
    }
    
    .stat-number {
        font-size: 1.6rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    /* Mobile navigation adjustments */
    .nav-link {
        padding: 14px 16px;
        font-size: 1rem;
    }
    
    /* Social links compact */
    .social-link {
        width: 40px;
        height: 40px;
        border-radius: 10px;
    }
    
    .social-link svg {
        width: 18px;
        height: 18px;
    }
}
