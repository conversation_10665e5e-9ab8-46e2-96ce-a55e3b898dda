/* Base Styles & Variables */
:root {
    --primary: #00A3FF;
    --primary-rgb: 0, 163, 255;
    --accent: #00FFD1;
    --accent-rgb: 0, 255, 209;
    --secondary: #001F3F;
    --text-light: #FFFFFF;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --transition: all 0.3s ease;
}

/* Reset & Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Vazirmatn', sans-serif;
    line-height: 1.6;
    background: var(--secondary);
    color: var(--text-light);
    overflow-x: hidden;
}

/* Glass Effect */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
}

/* Navigation */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1rem 0;
}

.floating-nav {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.nav-container:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

/* Logo Styles */
.logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-symbol {
    position: relative;
    width: 40px;
    height: 40px;
}

.logo-path {
    fill: none;
    stroke: url(#logo-gradient);
    stroke-width: 2;
    stroke-linecap: round;
}

.logo-orbit {
    fill: none;
    stroke: rgba(255, 255, 255, 0.1);
    stroke-width: 1;
    stroke-dasharray: 128;
    stroke-dashoffset: 128;
}

.energy-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    transform: translate(-50%, -50%);
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    gap: 0.5rem;
}

/* Navigation Links */
.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    position: relative;
    padding: 0.5rem 1rem;
    color: var(--text-light);
    text-decoration: none;
    overflow: hidden;
}

.nav-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.nav-text {
    position: relative;
    display: inline-block;
}

/* CTA Button */
.cta-button {
    position: relative;
    padding: 0.8rem 2rem;
    border-radius: 30px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border: none;
    cursor: pointer;
    overflow: hidden;
}

.button-text {
    position: relative;
    z-index: 2;
    color: white;
    font-weight: 500;
}

.button-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.button-arrow {
    width: 20px;
    height: 20px;
    fill: white;
    margin-right: 0.5rem;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--secondary) 0%, #000B18 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.geometric-shapes .shape {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(var(--primary-rgb), 0.1), rgba(var(--accent-rgb), 0.05));
    animation: shapeFloat 20s ease-in-out infinite;
}

.hero-content-wrapper {
    position: relative;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.energy-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.energy-line {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, 
        rgba(0, 163, 255, 0),
        rgba(0, 163, 255, 0.5),
        rgba(0, 255, 209, 0.8),
        rgba(0, 163, 255, 0.5),
        rgba(0, 163, 255, 0)
    );
    border-radius: 2px;
}

.section-icon {
    position: relative;
    width: 60px;
    height: 60px;
    margin-bottom: 1.5rem;
}

.icon-svg {
    width: 100%;
    height: 100%;
    fill: url(#gradient);
}

.section-title {
    background: linear-gradient(135deg, #00a3ff, #00ffd1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.main-description, .secondary-description {
    position: relative;
    z-index: 1;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    margin-bottom: 1rem;
}

.secondary-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95em;
}

/* گرادیان برای آیکون */
.pulse-effect {
    filter: drop-shadow(0 0 10px rgba(0, 163, 255, 0.5));
}

/* افکت هاور */
.hero-content-wrapper:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 163, 255, 0.15);
}

/* انیمیشن گرادیان پس‌زمینه */
@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, 
        rgba(0, 163, 255, 0.05),
        rgba(0, 255, 209, 0.05),
        rgba(0, 163, 255, 0.05)
    );
    background-size: 200% 200%;
    animation: gradientAnimation 15s ease infinite;
    z-index: 0;
}

.content-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.header-icon {
    position: relative;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00a3ff, #00ffd1);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.energy-pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    background: inherit;
    border-radius: inherit;
    animation: pulse 2s infinite;
}

.icon-svg {
    width: 32px;
    height: 32px;
    fill: white;
    z-index: 1;
    animation: float 3s ease-in-out infinite;
}

.content-title {
    font-size: 2.5rem;
    font-weight: 700;
}

.gradient-text {
    background: linear-gradient(135deg, #00a3ff, #00ffd1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientFlow 3s linear infinite;
}

.animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.energy-lines {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 45%, rgba(0, 163, 255, 0.1) 50%, transparent 55%);
    background-size: 200% 200%;
    animation: energyFlow 3s linear infinite;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle, #00a3ff 1px, transparent 1px);
    background-size: 50px 50px;
    animation: particleFloat 20s linear infinite;
}

.main-description, .secondary-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #00a3ff, #00ffd1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.1); opacity: 0.4; }
    100% { transform: scale(1); opacity: 0.8; }
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

@keyframes energyFlow {
    0% { background-position: 200% 200%; }
    100% { background-position: 0% 0%; }
}

@keyframes particleFloat {
    0% { transform: translateY(0); }
    100% { transform: translateY(-50px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-title {
        font-size: 2rem;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Buttons */
.primary-btn, .secondary-btn {
    padding: 12px 30px;
    border-radius: 30px;
    border: none;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.primary-btn {
    background: var(--gradient-1);
    color: var(--text-light);
}

.secondary-btn {
    background: var(--glass-bg);
    color: var(--text-light);
    border: 1px solid var(--glass-border);
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 163, 255, 0.2);
}

.secondary-btn:hover {
    background: var(--glass-border);
}

/* Achievement Cards */
.achievement-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 60px;
}

.achievement-card {
    padding: 30px;
    text-align: center;
    transition: var(--transition);
}

.achievement-card:hover {
    transform: translateY(-5px);
}

.achievement-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 20px;
    color: var(--primary);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: var(--gradient-1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* About Section */
.section {
    padding: 120px 0;
    position: relative;
    overflow: hidden;
}

.section-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.gradient-orb {
    position: absolute;
    width: 600px;
    height: 600px;
    border-radius: 50%;
    background: radial-gradient(circle at center, rgba(var(--primary-rgb), 0.1), transparent 70%);
    animation: orbFloat 20s ease-in-out infinite;
}

.section-content {
    position: relative;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
    z-index: 1;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-tag {
    display: inline-block;
    padding: 8px 20px;
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 20px;
}

.section-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.feature-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    padding: 30px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
}

.globe-container {
    position: relative;
    width: 100%;
    padding-top: 100%;
}

.interactive-globe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

@keyframes orbFloat {
    0%, 100% { transform: translate(0, 0); }
    50% { transform: translate(-30px, 30px); }
}

.feature-item {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.feature-icon {
    width: 40px;
    height: 40px;
    color: var(--primary);
}

/* Projects Grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.project-card {
    border-radius: 16px;
    overflow: hidden;
    position: relative;
}

.project-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-content {
    padding: 30px;
    position: relative;
}

.project-tags {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.tag {
    padding: 5px 15px;
    border-radius: 20px;
    background: var(--glass-bg);
    font-size: 0.9rem;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.service-card {
    padding: 40px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
}

.service-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 30px;
    color: var(--primary);
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.service-features {
    margin: 20px 0;
    padding: 0;
    list-style: none;
}

.service-features li {
    padding: 10px 0;
    padding-right: 25px;
    position: relative;
}

.service-features li::before {
    content: '→';
    position: absolute;
    right: 0;
    color: var(--primary);
}

.service-link {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--primary);
    text-decoration: none;
    margin-top: 20px;
    transition: gap 0.3s ease;
}

.service-link:hover {
    gap: 15px;
}

/* Contact Section */
.contact-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
}

.contact-form {
    padding: 40px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 20px;
    border-radius: 10px;
    border: 1px solid var(--glass-border);
    background: var(--glass-bg);
    color: var(--text-light);
    font-size: 1rem;
}

.form-group textarea {
    height: 150px;
    resize: none;
}

/* Footer */
.main-footer {
    padding: 60px 20px 30px;
    background: rgba(0, 0, 0, 0.2);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 3fr;
    gap: 60px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.footer-column h3 {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: var(--text-light);
    text-decoration: none;
    opacity: 0.8;
    transition: var(--transition);
}

.footer-column ul li a:hover {
    opacity: 1;
    color: var(--primary);
}

.footer-bottom {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid var(--glass-border);
    text-align: center;
    opacity: 0.7;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

[data-aos] {
    opacity: 0;
    transform: translateY(20px);
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 3.5rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .about-grid {
        grid-template-columns: 1fr;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .hero-title {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-cta {
        flex-direction: column;
    }
    
    .section {
        padding: 80px 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .service-card {
        padding: 30px;
    }
}

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.3s;
}

.energy-loader {
    width: 60px;
    height: 60px;
    border: 3px solid transparent;
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.scroll-indicator:hover {
    opacity: 1;
}

.scroll-line {
    width: 2px;
    height: 60px;
    background: var(--primary);
    transform-origin: top;
    animation: scrollLine 2s ease-in-out infinite;
}

@keyframes scrollLine {
    0% { transform: scaleY(0); opacity: 0; }
    50% { transform: scaleY(1); opacity: 1; }
    100% { transform: scaleY(0); opacity: 0; }
}

@media (max-width: 768px) {
    .hero-title { font-size: 2.5rem; }
    .hero-stats { grid-template-columns: 1fr; }
    .hero-cta { flex-direction: column; }
}

/* Hero Grid Layout */
.hero-grid {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 4rem;
    align-items: center;
    direction: rtl;
}

.hero-text-column {
    position: relative;
    padding: 2rem;
    z-index: 2;
}

/* Logo Animation Styles */
.logo-animation-container {
    position: relative;
    width: 100%;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.logo-wrapper {
    position: relative;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: rgba(0, 31, 63, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 50px rgba(0, 163, 255, 0.2);
}

/* لوگوی اصلی */
.logo-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    z-index: 10;
}

.main-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    animation: logoFloat 6s ease-in-out infinite;
}

/* حلقه‌های انرژی */
.logo-energy-rings {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.energy-ring {
    position: absolute;
    border-radius: 50%;
    border: 2px solid transparent;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring-1 { width: 90%; height: 90%; animation: ringPulse 3s ease-in-out infinite; }
.ring-2 { width: 80%; height: 80%; animation: ringPulse 3s ease-in-out infinite 0.6s; }
.ring-3 { width: 70%; height: 70%; animation: ringPulse 3s ease-in-out infinite 1.2s; }
.ring-4 { width: 60%; height: 60%; animation: ringPulse 3s ease-in-out infinite 1.8s; }
.ring-5 { width: 50%; height: 50%; animation: ringPulse 3s ease-in-out infinite 2.4s; }

/* افکت‌های نور */
.energy-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.energy-glow {
    position: absolute;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle,
        rgba(0, 163, 255, 0.2) 0%,
        rgba(0, 255, 209, 0.1) 30%,
        transparent 70%
    );
    animation: glowPulse 4s ease-in-out infinite;
}

/* مسیرهای مداری */
.orbital-paths {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: orbitalRotation 20s linear infinite;
}

.orbit {
    fill: none;
    stroke: url(#logo-gradient);
    stroke-width: 1;
    stroke-dasharray: 10 5;
    opacity: 0.5;
}

/* انیمیشن‌ها */
@keyframes logoFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

@keyframes ringPulse {
    0% { 
        transform: translate(-50%, -50%) scale(0.8);
        border-color: rgba(0, 163, 255, 0.1);
    }
    50% { 
        transform: translate(-50%, -50%) scale(1);
        border-color: rgba(0, 255, 209, 0.3);
    }
    100% { 
        transform: translate(-50%, -50%) scale(0.8);
        border-color: rgba(0, 163, 255, 0.1);
    }
}

@keyframes glowPulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes orbitalRotation {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ذرات معلق */
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: particleFloat 8s ease-in-out infinite;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--primary);
}

/* افکت‌های پیشرفته */
.advanced-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    mix-blend-mode: screen;
}

/* رسپانسیو */
@media (max-width: 768px) {
    .logo-wrapper {
        width: 300px;
        height: 300px;
    }

    .logo-container {
        width: 150px;
        height: 150px;
    }
}

/* انیمیشن عنوان */
.title-animation-wrapper {
    overflow: hidden;
}

.title-line {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.animated-word {
    display: inline-block;
    opacity: 0;
    transform: translateY(100%);
}

.gradient-text {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* استایل توضیحات */
.hero-description-wrapper {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.description-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.description-divider {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), transparent);
    margin: 1rem 0;
    overflow: hidden;
}

.divider-icon {
    position: absolute;
    width: 24px;
    height: 24px;
    fill: var(--primary);
    animation: moveIcon 3s linear infinite;
}

/* دکمه‌های CTA */
.hero-cta {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
}

.cta-button {
    position: relative;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cta-button.primary {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    color: white;
}

.button-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

/* آمارها */
.stat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

/* انیمیشن‌ها */
@keyframes moveIcon {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Hero Text Styles */
.hero-text-column {
    padding: 3rem;
    position: relative;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.3;
    margin-bottom: 2.5rem;
}

.title-line {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    justify-content: flex-start;
}

.animated-word {
    display: inline-block;
    position: relative;
    font-family: 'Vazirmatn', sans-serif;
    opacity: 0;
    transform: translateY(50px);
    color: var(--text-light);
}

.gradient-text {
    background: linear-gradient(135deg, #00a3ff, #00ff9d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

/* Description Card Styles */
.hero-description-wrapper {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 2.5rem;
    margin: 3rem 0;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.description-header {
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.text-accent {
    color: #00a3ff;
    font-weight: 800;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 2;
    color: var(--text-light);
    text-align: justify;
    opacity: 0;
}

/* Button Styles */
.hero-cta {
    display: flex;
    gap: 1.5rem;
    margin-top: 3rem;
}

.cta-button {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    font-family: 'Vazirmatn', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cta-button.primary {
    background: linear-gradient(135deg, #00a3ff, #00ff9d);
    padding: 1.2rem 2.5rem;
    border: none;
    color: white;
}

.cta-button.secondary {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.1);
    padding: calc(1.2rem - 2px) calc(2.5rem - 2px);
    color: var(--text-light);
}

.button-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.button-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 60%);
    opacity: 0;
}

/* Stats Grid Styles */
.hero-stats {
    margin-top: 4rem;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transform: translateY(30px);
    opacity: 0;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #00a3ff;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Hover Effects & Animations */
.cta-button.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 163, 255, 0.2);
}

.cta-button.secondary:hover {
    border-color: #00a3ff;
    transform: translateY(-3px);
}

.hero-description-wrapper:hover {
    transform: translate3d(0, -5px, 20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

@keyframes floatAnimation {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes glowAnimation {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Section Styles */
.content-section {
    padding: 1.5rem;
    border-radius: 16px;
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
    transition: transform 0.3s ease;
}

.content-section:hover {
    transform: translateY(-5px);
}

.section-icon {
    position: relative;
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-svg {
    width: 24px;
    height: 24px;
    fill: white;
    z-index: 1;
}

.icon-pulse {
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: inherit;
    opacity: 0.5;
    animation: pulse 2s infinite;
}

.section-content {
    flex: 1;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    color: transparent;
}

/* Reasons Grid */
.reasons-grid {
    margin-top: 2rem;
}

.reasons-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.reasons-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.reason-card {
    padding: 1.25rem;
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.3s ease;
}

.reason-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
}

.reason-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.reason-card p {
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.2;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .section-icon {
        margin-bottom: 1rem;
    }

    .reasons-items {
        grid-template-columns: 1fr;
    }

    .reason-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

/* Glass Morphism */
.neo-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Text Styles */
.split-text {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* Hero Grid Layout */
.hero-grid {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 4rem;
    align-items: center;
    direction: rtl;
}

.hero-text-column {
    position: relative;
    padding: 2rem;
    z-index: 2;
}

/* Logo Animation Styles */
.logo-animation-container {
    position: relative;
    width: 100%;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.logo-wrapper {
    position: relative;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: rgba(0, 31, 63, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 50px rgba(0, 163, 255, 0.2);
}

/* لوگوی اصلی */
.logo-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    z-index: 10;
}

.main-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    animation: logoFloat 6s ease-in-out infinite;
}

/* حلقه‌های انرژی */
.logo-energy-rings {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.energy-ring {
    position: absolute;
    border-radius: 50%;
    border: 2px solid transparent;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring-1 { width: 90%; height: 90%; animation: ringPulse 3s ease-in-out infinite; }
.ring-2 { width: 80%; height: 80%; animation: ringPulse 3s ease-in-out infinite 0.6s; }
.ring-3 { width: 70%; height: 70%; animation: ringPulse 3s ease-in-out infinite 1.2s; }
.ring-4 { width: 60%; height: 60%; animation: ringPulse 3s ease-in-out infinite 1.8s; }
.ring-5 { width: 50%; height: 50%; animation: ringPulse 3s ease-in-out infinite 2.4s; }

/* افکت‌های نور */
.energy-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.energy-glow {
    position: absolute;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle,
        rgba(0, 163, 255, 0.2) 0%,
        rgba(0, 255, 209, 0.1) 30%,
        transparent 70%
    );
    animation: glowPulse 4s ease-in-out infinite;
}

/* مسیرهای مداری */
.orbital-paths {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: orbitalRotation 20s linear infinite;
}

.orbit {
    fill: none;
    stroke: url(#logo-gradient);
    stroke-width: 1;
    stroke-dasharray: 10 5;
    opacity: 0.5;
}

/* انیمیشن‌ها */
@keyframes logoFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

@keyframes ringPulse {
    0% { 
        transform: translate(-50%, -50%) scale(0.8);
        border-color: rgba(0, 163, 255, 0.1);
    }
    50% { 
        transform: translate(-50%, -50%) scale(1);
        border-color: rgba(0, 255, 209, 0.3);
    }
    100% { 
        transform: translate(-50%, -50%) scale(0.8);
        border-color: rgba(0, 163, 255, 0.1);
    }
}

@keyframes glowPulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes orbitalRotation {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ذرات معلق */
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: particleFloat 8s ease-in-out infinite;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--primary);
}

/* افکت‌های پیشرفته */
.advanced-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    mix-blend-mode: screen;
}

/* رسپانسیو */
@media (max-width: 768px) {
    .logo-wrapper {
        width: 300px;
        height: 300px;
    }

    .logo-container {
        width: 150px;
        height: 150px;
    }
}

/* انیمیشن عنوان */
.title-animation-wrapper {
    overflow: hidden;
}

.title-line {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.animated-word {
    display: inline-block;
    opacity: 0;
    transform: translateY(100%);
}

.gradient-text {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* استایل توضیحات */
.hero-description-wrapper {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.description-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.description-divider {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), transparent);
    margin: 1rem 0;
    overflow: hidden;
}

.divider-icon {
    position: absolute;
    width: 24px;
    height: 24px;
    fill: var(--primary);
    animation: moveIcon 3s linear infinite;
}

/* دکمه‌های CTA */
.hero-cta {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
}

.cta-button {
    position: relative;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cta-button.primary {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    color: white;
}

.button-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

/* آمارها */
.stat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

/* انیمیشن‌ها */
@keyframes moveIcon {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Hero Text Styles */
.hero-text-column {
    padding: 3rem;
    position: relative;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.3;
    margin-bottom: 2.5rem;
}

.title-line {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    justify-content: flex-start;
}

.animated-word {
    display: inline-block;
    position: relative;
    font-family: 'Vazirmatn', sans-serif;
    opacity: 0;
    transform: translateY(50px);
    color: var(--text-light);
}

.gradient-text {
    background: linear-gradient(135deg, #00a3ff, #00ff9d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

/* Description Card Styles */
.hero-description-wrapper {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 2.5rem;
    margin: 3rem 0;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.description-header {
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.text-accent {
    color: #00a3ff;
    font-weight: 800;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 2;
    color: var(--text-light);
    text-align: justify;
    opacity: 0;
}

/* Button Styles */
.hero-cta {
    display: flex;
    gap: 1.5rem;
    margin-top: 3rem;
}

.cta-button {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    font-family: 'Vazirmatn', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cta-button.primary {
    background: linear-gradient(135deg, #00a3ff, #00ff9d);
    padding: 1.2rem 2.5rem;
    border: none;
    color: white;
}

.cta-button.secondary {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.1);
    padding: calc(1.2rem - 2px) calc(2.5rem - 2px);
    color: var(--text-light);
}

.button-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.button-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 60%);
    opacity: 0;
}

/* Stats Grid Styles */
.hero-stats {
    margin-top: 4rem;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transform: translateY(30px);
    opacity: 0;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #00a3ff;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Hover Effects & Animations */
.cta-button.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 163, 255, 0.2);
}

.cta-button.secondary:hover {
    border-color: #00a3ff;
    transform: translateY(-3px);
}

.hero-description-wrapper:hover {
    transform: translate3d(0, -5px, 20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

@keyframes floatAnimation {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes glowAnimation {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Section Styles */
.content-section {
    padding: 1.5rem;
    border-radius: 16px;
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
    transition: transform 0.3s ease;
}

.content-section:hover {
    transform: translateY(-5px);
}

.section-icon {
    position: relative;
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-svg {
    width: 24px;
    height: 24px;
    fill: white;
    z-index: 1;
}

.icon-pulse {
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: inherit;
    opacity: 0.5;
    animation: pulse 2s infinite;
}

.section-content {
    flex: 1;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    color: transparent;
}

/* Reasons Grid */
.reasons-grid {
    margin-top: 2rem;
}

.reasons-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.reasons-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.reason-card {
    padding: 1.25rem;
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.3s ease;
}

.reason-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
}

.reason-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.reason-card p {
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.2;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .section-icon {
        margin-bottom: 1rem;
    }

    .reasons-items {
        grid-template-columns: 1fr;
    }

    .reason-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

/* Glass Morphism */
.neo-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Text Styles */
.split-text {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* Hero Grid Layout */
.hero-grid {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 4rem;
    align-items: center;
    direction: rtl;
}

.hero-text-column {
    position: relative;
    padding: 2rem;
    z-index: 2;
}

/* Logo Animation Styles */
.logo-animation-container {
    position: relative;
    width: 100%;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.logo-wrapper {
    position: relative;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: rgba(0, 31, 63, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 50px rgba(0, 163, 255, 0.2);
}

/* لوگوی اصلی */
.logo-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    z-index: 10;
}

.main-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    animation: logoFloat 6s ease-in-out infinite;
}

/* حلقه‌های انرژی */
.logo-energy-rings {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.energy-ring {
    position: absolute;
    border-radius: 50%;
    border: 2px solid transparent;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring-1 { width: 90%; height: 90%; animation: ringPulse 3s ease-in-out infinite; }
.ring-2 { width: 80%; height: 80%; animation: ringPulse 3s ease-in-out infinite 0.6s; }
.ring-3 { width: 70%; height: 70%; animation: ringPulse 3s ease-in-out infinite 1.2s; }
.ring-4 { width: 60%; height: 60%; animation: ringPulse 3s ease-in-out infinite 1.8s; }
.ring-5 { width: 50%; height: 50%; animation: ringPulse 3s ease-in-out infinite 2.4s; }

/* افکت‌های نور */
.energy-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.energy-glow {
    position: absolute;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle,
        rgba(0, 163, 255, 0.2) 0%,
        rgba(0, 255, 209, 0.1) 30%,
        transparent 70%
    );
    animation: glowPulse 4s ease-in-out infinite;
}

/* مسیرهای مداری */
.orbital-paths {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: orbitalRotation 20s linear infinite;
}

.orbit {
    fill: none;
    stroke: url(#logo-gradient);
    stroke-width: 1;
    stroke-dasharray: 10 5;
    opacity: 0.5;
}

/* انیمیشن‌ها */
@keyframes logoFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

@keyframes ringPulse {
    0% { 
        transform: translate(-50%, -50%) scale(0.8);
        border-color: rgba(0, 163, 255, 0.1);
    }
    50% { 
        transform: translate(-50%, -50%) scale(1);
        border-color: rgba(0, 255, 209, 0.3);
    }
    100% { 
        transform: translate(-50%, -50%) scale(0.8);
        border-color: rgba(0, 163, 255, 0.1);
    }
}

@keyframes glowPulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes orbitalRotation {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ذرات معلق */
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: particleFloat 8s ease-in-out infinite;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--primary);
}

/* افکت‌های پیشرفته */
.advanced-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    mix-blend-mode: screen;
}

/* رسپانسیو */
@media (max-width: 768px) {
    .logo-wrapper {
        width: 300px;
        height: 300px;
    }

    .logo-container {
        width: 150px;
        height: 150px;
    }
}

/* انیمیشن عنوان */
.title-animation-wrapper {
    overflow: hidden;
}

.title-line {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.animated-word {
    display: inline-block;
    opacity: 0;
    transform: translateY(100%);
}

.gradient-text {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* استایل توضیحات */
.hero-description-wrapper {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.description-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.description-divider {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), transparent);
    margin: 1rem 0;
    overflow: hidden;
}

.divider-icon {
    position: absolute;
    width: 24px;
    height: 24px;
    fill: var(--primary);
    animation: moveIcon 3s linear infinite;
}

/* دکمه‌های CTA */
.hero-cta {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
}

.cta-button {
    position: relative;
    padding: 1rem 2rem;
    border: none;
}
/* رسپانسیو */
@media (max-width: 768px) {
    .logo-wrapper {
        width: 300px;
        height: 300px;
    }

    .logo-container {
        width: 150px;
        height: 150px;
    }
}

/* انیمیشن عنوان */
.title-animation-wrapper {
    overflow: hidden;
}

.title-line {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.animated-word {
    display: inline-block;
    opacity: 0;
    transform: translateY(100%);
}

.gradient-text {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* استایل توضیحات */
.hero-description-wrapper {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.description-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.description-divider {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), transparent);
    margin: 1rem 0;
    overflow: hidden;
}

.divider-icon {
    position: absolute;
    width: 24px;
    height: 24px;
    fill: var(--primary);
    animation: moveIcon 3s linear infinite;
}

/* دکمه‌های CTA */
.hero-cta {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
}

.cta-button {
    position: relative;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cta-button.primary {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    color: white;
}

.button-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

/* آمارها */
.stat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

/* انیمیشن‌ها */
@keyframes moveIcon {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Hero Text Styles */
.hero-text-column {
    padding: 3rem;
    position: relative;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.3;
    margin-bottom: 2.5rem;
}

.title-line {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    justify-content: flex-start;
}

.animated-word {
    display: inline-block;
    position: relative;
    font-family: 'Vazirmatn', sans-serif;
    opacity: 0;
    transform: translateY(50px);
    color: var(--text-light);
}

.gradient-text {
    background: linear-gradient(135deg, #00a3ff, #00ff9d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

/* Description Card Styles */
.hero-description-wrapper {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 2.5rem;
    margin: 3rem 0;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.description-header {
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.text-accent {
    color: #00a3ff;
    font-weight: 800;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 2;
    color: var(--text-light);
    text-align: justify;
    opacity: 0;
}

/* Button Styles */
.hero-cta {
    display: flex;
    gap: 1.5rem;
    margin-top: 3rem;
}

.cta-button {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    font-family: 'Vazirmatn', sans-serif;
    font-weight: 60;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.description-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.description-divider {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), transparent);
    margin: 1rem 0;
    overflow: hidden;
}

.divider-icon {
    position: absolute;
    width: 24px;
    height: 24px;
    fill: var(--primary);
    animation: moveIcon 3s linear infinite;
}

/* دکمه‌های CTA */
.hero-cta {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
}

.cta-button {
    position: relative;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cta-button.primary {
    background: linear-gradient(45deg, var(--primary), var(--accent));
    color: white;
}

.button-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

/* آمارها */
.stat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

/* انیمیشن‌ها */
@keyframes moveIcon {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Hero Text Styles */
.hero-text-column {
    padding: 3rem;
    position: relative;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.3;
    margin-bottom: 2.5rem;
}

.title-line {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    justify-content: flex-start;
}

.animated-word {
    display: inline-block;
    position: relative;
    font-family: 'Vazirmatn', sans-serif;
    opacity: 0;
    transform: translateY(50px);
    color: var(--text-light);
}

.gradient-text {
    background: linear-gradient(135deg, #00a3ff, #00ff9d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

/* Description Card Styles */
.hero-description-wrapper {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 2.5rem;
    margin: 3rem 0;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.description-header {
    margin-bottom: 1.5rem;
}

.description-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.text-accent {
    color: #00a3ff;
    font-weight: 800;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 2;
    color: var(--text-light);
    text-align: justify;
    opacity: 0;
}

/* Button Styles */
.hero-cta {
    display: flex;
    gap: 1.5rem;
    margin-top: 3rem;
}

.cta-button {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    font-family: 'Vazirmatn', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cta-button.primary {
    background: linear-gradient(135deg, #00a3ff, #00ff9d);
    padding: 1.2rem 2.5rem;
    border: none;
    color: white;
}

.cta-button.secondary {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.1);
    padding: calc(1.2rem - 2px) calc(2.5rem - 2px);
    color: var(--text-light);
}

.button-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.button-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 60%);
    opacity: 0;
}

/* Stats Grid Styles */
.hero-stats {
    margin-top: 4rem;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transform: translateY(30px);
    opacity: 0;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #00a3ff;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Hover Effects & Animations */
.cta-button.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 163, 255, 0.2);
}

.cta-button.secondary:hover {
    border-color: #00a3ff;
    transform: translateY(-3px);
}

.hero-description-wrapper:hover {
    transform: translate3d(0, -5px, 20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

@keyframes floatAnimation {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes glowAnimation {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Section Styles */
.content-section {
    padding: 1.5rem;
    border-radius: 16px;
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
    transition: transform 0.3s ease;
}

.content-section:hover {
    transform: translateY(-5px);
}

.section-icon {
    position: relative;
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

