// filepath: c:\wamp\www\inova2\js\feature-circles.js

document.addEventListener('DOMContentLoaded', function() {
    const circles = document.querySelectorAll('.feature-circle');

    circles.forEach(circle => {
        circle.addEventListener('mouseenter', () => {
            circle.classList.add('hover');
        });

        circle.addEventListener('mouseleave', () => {
            circle.classList.remove('hover');
        });
    });

    // Smooth scroll to sections
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            const headerOffset = 100; // Adjust based on your header height
            const elementPosition = targetElement.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        });
    });
});