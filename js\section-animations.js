class SectionAnimations {
    constructor() {
        this.initParallax();
        this.initParticles();
        this.initScrollAnimations();
    }

    initParallax() {
        document.querySelectorAll('.energy-section').forEach(section => {
            const content = section.querySelector('.section-content');
            
            section.addEventListener('mousemove', e => {
                const rect = section.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const deltaX = (mouseX - centerX) / 25;
                const deltaY = (mouseY - centerY) / 25;

                gsap.to(content, {
                    x: deltaX,
                    y: deltaY,
                    duration: 1,
                    ease: 'power2.out'
                });
            });

            section.addEventListener('mouseleave', () => {
                gsap.to(content, {
                    x: 0,
                    y: 0,
                    duration: 1,
                    ease: 'power2.out'
                });
            });
        });
    }

    initParticles() {
        const canvases = document.querySelectorAll('.particle-canvas');
        
        canvases.forEach(canvas => {
            const type = canvas.dataset.type;
            const ctx = canvas.getContext('2d');
            const particles = [];
            
            // Set canvas size
            const resizeCanvas = () => {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            };
            
            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);

            // Create particles based on section type
            for(let i = 0; i < 100; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    size: Math.random() * 3 + 1,
                    speedX: Math.random() * 2 - 1,
                    speedY: Math.random() * 2 - 1,
                    type: type
                });
            }

            // Animation loop
            const animate = () => {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                particles.forEach(particle => {
                    // Update particle position
                    particle.x += particle.speedX;
                    particle.y += particle.speedY;

                    // Wrap particles around screen
                    if(particle.x < 0) particle.x = canvas.width;
                    if(particle.x > canvas.width) particle.x = 0;
                    if(particle.y < 0) particle.y = canvas.height;
                    if(particle.y > canvas.height) particle.y = 0;

                    // Draw particle with type-specific color
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fillStyle = this.getParticleColor(particle.type);
                    ctx.fill();
                });

                requestAnimationFrame(animate);
            };

            animate();
        });
    }

    getParticleColor(type) {
        const colors = {
            solar: 'rgba(255, 190, 0, 0.8)',
            wind: 'rgba(0, 191, 255, 0.8)',
            hydrogen: 'rgba(0, 255, 127, 0.8)',
            storage: 'rgba(147, 112, 219, 0.8)'
        };
        return colors[type] || 'rgba(255, 255, 255, 0.8)';
    }

    initScrollAnimations() {
        gsap.registerPlugin(ScrollTrigger);

        document.querySelectorAll('.energy-section').forEach((section, index) => {
            gsap.from(section, {
                scrollTrigger: {
                    trigger: section,
                    start: 'top center',
                    end: 'bottom center',
                    scrub: 1,
                    toggleActions: 'play none none reverse'
                },
                opacity: 0,
                y: 100,
                scale: 0.9,
                duration: 1.5
            });

            // Animate section content
            const content = section.querySelector('.section-content');
            gsap.from(content, {
                scrollTrigger: {
                    trigger: section,
                    start: 'top center',
                    end: 'bottom center',
                    scrub: 1
                },
                opacity: 0,
                scale: 0.8,
                rotation: 5,
                duration: 1
            });
        });
    }
}

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SectionAnimations();
});

// اضافه کردن انیمیشن‌های جدید برای بخش rebranding
function initRebrandingAnimations() {
    // ایجاد ذرات انرژی
    const energyFlow = document.querySelector('.energy-flow-visualization');
    if (energyFlow) {
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'energy-particle';
            particle.style.top = `${Math.random() * 100}%`;
            particle.style.animationDelay = `${Math.random() * 4}s`;
            energyFlow.appendChild(particle);
        }
    }

    // انیمیشن showcase items
    const showcaseItems = document.querySelectorAll('.showcase-item');
    showcaseItems.forEach((item, index) => {
        const xPos = 50 + Math.cos(index * (Math.PI * 2 / showcaseItems.length)) * 200;
        const yPos = 50 + Math.sin(index * (Math.PI * 2 / showcaseItems.length)) * 200;
        
        gsap.set(item, {
            x: xPos,
            y: yPos,
            opacity: 0,
            scale: 0.8
        });

        gsap.to(item, {
            opacity: 1,
            scale: 1,
            duration: 1,
            delay: index * 0.2,
            ease: "power2.out"
        });
    });

    // Intersection Observer برای timeline points
    const timelinePoints = document.querySelectorAll('.timeline-point');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = 1;
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, { threshold: 0.5 });

    timelinePoints.forEach(point => {
        point.style.opacity = 0;
        point.style.transform = 'translateX(-50px)';
        observer.observe(point);
    });
}

// اجرای انیمیشن‌ها
document.addEventListener('DOMContentLoaded', () => {
    initRebrandingAnimations();
});
