/* Responsive styles for the website components */

@media (max-width: 1200px) {
    body {
        font-size: 16px;
    }

    .header {
        padding: 10px;
    }

    .hero {
        height: 60vh;
        background-size: cover;
    }

    .about, .activities, .rebranding {
        padding: 20px;
    }

    .section-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .header {
        flex-direction: column;
        align-items: center;
    }

    .hero {
        height: 50vh;
    }

    .about, .activities, .rebranding {
        padding: 15px;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    body {
        font-size: 14px;
    }

    .hero {
        height: 40vh;
    }

    .about, .activities, .rebranding {
        padding: 10px;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .feature-circle {
        width: 80%;
        margin: 0 auto;
    }
}

@media (max-width: 576px) {
    .header {
        padding: 5px;
    }

    .hero {
        height: 30vh;
    }

    .about, .activities, .rebranding {
        padding: 5px;
    }

    .section-title {
        font-size: 1.2rem;
    }

    .feature-circle {
        width: 100%;
    }
}

/* Smooth transitions between sections */
.section {
    transition: all 0.5s ease-in-out;
}

.section-enter {
    opacity: 0;
    transform: translateY(20px);
}

.section-enter-active {
    opacity: 1;
    transform: translateY(0);
}

.section-exit {
    opacity: 1;
    transform: translateY(0);
}

.section-exit-active {
    opacity: 0;
    transform: translateY(-20px);
}