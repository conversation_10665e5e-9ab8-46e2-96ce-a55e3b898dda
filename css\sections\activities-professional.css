/* ===================================
   PROJECTS SECTION - PROFESSIONAL DESIGN
   Ultra-advanced styling with cutting-edge animations
   =================================== */

/* Projects Section Base */
.projects-section-professional {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 1) 0%,
            rgba(0, 31, 63, 1) 30%,
            rgba(0, 15, 30, 1) 70%,
            rgba(0, 31, 63, 1) 100%);
    overflow: hidden;
}

/* Projects Container */
.projects-container-professional {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Projects Grid */
.projects-grid-professional {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin: 80px 0;
}

/* Project Item */
.project-item-professional {
    opacity: 0;
    transform: translateY(50px);
    animation: projectReveal 1.5s ease-out forwards;
}

.project-item-professional:nth-child(1) {
    animation-delay: 1.5s;
}

.project-item-professional:nth-child(2) {
    animation-delay: 1.7s;
}

.project-item-professional:nth-child(3) {
    animation-delay: 1.9s;
}

.project-item-professional:nth-child(4) {
    animation-delay: 2.1s;
}

/* Project Card */
.project-card-professional {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(25px);
    height: 100%;
}

.project-card-professional:hover {
    transform: translateY(-15px);
    border-color: rgba(0, 255, 209, 0.4);
    box-shadow: 0 30px 100px rgba(0, 163, 255, 0.3);
}

/* Project Image Container */
.project-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.1) 0%,
            rgba(0, 255, 209, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-icon {
    width: 80px;
    height: 80px;
    color: rgba(255, 255, 255, 0.3);
}

.image-icon svg {
    width: 100%;
    height: 100%;
}

/* Project Overlay */
.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 0.95) 0%,
            rgba(0, 31, 63, 0.95) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30px;
}

.project-card-professional:hover .project-overlay {
    opacity: 1;
}

/* Overlay Content */
.overlay-content {
    flex: 1;
}

.project-category {
    display: inline-block;
    padding: 6px 15px;
    background: rgba(0, 255, 209, 0.2);
    color: var(--accent);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 15px;
    border: 1px solid rgba(0, 255, 209, 0.3);
}

.project-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 15px;
    line-height: 1.3;
}

.project-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
}

/* Project Stats */
.project-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Project Link */
.project-link-container {
    margin-top: auto;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--accent);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.project-link:hover {
    gap: 12px;
    color: var(--primary);
}

.link-arrow {
    transition: transform 0.3s ease;
}

.project-link:hover .link-arrow {
    transform: translateX(4px);
}

.link-arrow svg {
    width: 16px;
    height: 16px;
}

@keyframes projectReveal {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===================================
   ACTIVITIES SECTION - PROFESSIONAL DESIGN
   Ultra-advanced styling with cutting-edge animations
   =================================== */

/* Activities Section Base */
.activities-section-fixed {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg,
            rgba(0, 31, 63, 1) 0%,
            rgba(0, 15, 30, 1) 30%,
            rgba(0, 31, 63, 1) 70%,
            rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* Advanced Background System */
.activities-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse at 30% 20%, rgba(0, 255, 209, 0.12) 0%, transparent 50%),
        radial-gradient(ellipse at 70% 80%, rgba(0, 163, 255, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse at 50% 50%, rgba(0, 255, 209, 0.06) 0%, transparent 70%);
    animation: gradientPulse 15s ease-in-out infinite;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(45deg, rgba(0, 255, 209, 0.04) 1px, transparent 1px),
        linear-gradient(-45deg, rgba(0, 163, 255, 0.04) 1px, transparent 1px);
    background-size: 80px 80px, 60px 60px;
    animation: patternMove 30s linear infinite;
    opacity: 0.5;
}

.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape-1,
.shape-2 {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.08),
            rgba(0, 163, 255, 0.08));
    animation: shapeFloat 20s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    right: 15%;
    animation-delay: 10s;
}

/* Activities Container */
.activities-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.activities-header {
    text-align: center;
    margin-bottom: 100px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 12px 28px;
    background: rgba(0, 255, 209, 0.1);
    border: 1px solid rgba(0, 255, 209, 0.3);
    border-radius: 50px;
    color: var(--accent);
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 40px;
    backdrop-filter: blur(15px);
    box-shadow: 0 10px 40px rgba(0, 255, 209, 0.15);
    animation: badgeGlow 4s ease-in-out infinite;
}

.badge-icon {
    font-size: 1.5rem;
    animation: iconSpin 6s linear infinite;
}

.section-title {
    font-size: clamp(2.5rem, 6vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 40px;
    color: var(--text-light);
    text-shadow: 0 4px 25px rgba(0, 163, 255, 0.4);
}

.title-main {
    display: block;
    font-size: 1em;
    margin-bottom: 15px;
    opacity: 0;
    transform: translateY(50px) scale(0.8);
    animation: titleReveal 1.8s ease-out 0.5s forwards;
}

.title-sub {
    display: block;
    font-size: 1.2em;
    margin-bottom: 15px;
    opacity: 0;
    transform: translateY(50px) scale(0.8);
    animation: titleReveal 1.8s ease-out 0.8s forwards;
}

.gradient-text {
    background: linear-gradient(135deg,
            var(--primary) 0%,
            var(--accent) 30%,
            #00FFD1 60%,
            var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 400% 400%;
    animation: gradientShift 5s ease-in-out infinite;
}

.section-description {
    font-size: clamp(1.2rem, 3vw, 1.6rem);
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.85);
    max-width: 900px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1.8s ease-out 1.2s forwards;
}

/* Activities Grid */
.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 50px;
    margin: 100px 0;
}

/* Activity Item */
.activity-item {
    opacity: 0;
    transform: translateY(60px) rotateX(15deg);
    animation: activityReveal 1.5s ease-out forwards;
}

.activity-item:nth-child(1) {
    animation-delay: 1.5s;
}

.activity-item:nth-child(2) {
    animation-delay: 1.7s;
}

.activity-item:nth-child(3) {
    animation-delay: 1.9s;
}

.activity-item:nth-child(4) {
    animation-delay: 2.1s;
}

.activity-item:nth-child(5) {
    animation-delay: 2.3s;
}

.activity-item:nth-child(6) {
    animation-delay: 2.5s;
}

.activity-item:nth-child(7) {
    animation-delay: 2.7s;
}

/* Activity Card */
.activity-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 50px;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(25px);
    overflow: hidden;
    perspective: 1000px;
}

.activity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.05) 0%,
            rgba(0, 163, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.6s ease;
    border-radius: 25px;
}

.activity-card:hover::before {
    opacity: 1;
}

.activity-card:hover {
    transform: translateY(-15px) rotateX(5deg) rotateY(5deg);
    border-color: rgba(0, 255, 209, 0.4);
    box-shadow:
        0 30px 100px rgba(0, 163, 255, 0.25),
        0 0 50px rgba(0, 255, 209, 0.2);
}

/* Card Header */
.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 35px;
    position: relative;
    z-index: 2;
}

.activity-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.4s ease;
    box-shadow: 0 15px 40px rgba(0, 163, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.activity-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.2),
            transparent);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.activity-card:hover .activity-icon::before {
    opacity: 1;
}

.activity-icon svg {
    width: 36px;
    height: 36px;
    transition: transform 0.4s ease;
}

.activity-card:hover .activity-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 20px 50px rgba(0, 163, 255, 0.4);
}

.activity-card:hover .activity-icon svg {
    transform: scale(1.1);
}

.activity-number {
    font-size: 1.4rem;
    font-weight: 800;
    color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.05);
    width: 50px;
    height: 50px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
}

.activity-card:hover .activity-number {
    color: var(--accent);
    background: rgba(0, 255, 209, 0.1);
    border-color: rgba(0, 255, 209, 0.3);
    transform: scale(1.1);
}

/* Card Content */
.card-content {
    flex: 1;
    margin-bottom: 35px;
    position: relative;
    z-index: 2;
}

.activity-title {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.3;
    transition: color 0.4s ease;
}

.activity-card:hover .activity-title {
    color: var(--accent);
}

.activity-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
}

/* Activity Features */
.activity-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.85);
    transition: all 0.3s ease;
}

.feature-item:hover {
    color: var(--accent);
    transform: translateX(10px);
}

.feature-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    font-weight: 700;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(0, 163, 255, 0.3);
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    transform: scale(1.2);
    box-shadow: 0 8px 25px rgba(0, 163, 255, 0.4);
}

/* CTA Section */
.activities-cta {
    text-align: center;
    margin-top: 100px;
    padding: 80px 50px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    backdrop-filter: blur(25px);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(50px);
    animation: fadeInUp 1.5s ease-out 3s forwards;
}

.activities-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 255, 209, 0.03) 0%,
            rgba(0, 163, 255, 0.03) 100%);
    animation: ctaGlow 6s ease-in-out infinite;
}

.cta-content {
    position: relative;
    z-index: 2;
    max-width: 700px;
    margin: 0 auto;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-light);
    margin-bottom: 20px;
    text-shadow: 0 2px 15px rgba(0, 163, 255, 0.3);
}

.cta-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 20px 40px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: var(--text-light);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.4s ease;
    box-shadow: 0 15px 40px rgba(0, 163, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.2),
            transparent);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.cta-button:hover::before {
    opacity: 1;
}

.cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 25px 60px rgba(0, 163, 255, 0.4);
}

.button-arrow {
    transition: transform 0.4s ease;
}

.cta-button:hover .button-arrow {
    transform: translateX(8px);
}

/* Animations */
@keyframes gradientPulse {

    0%,
    100% {
        opacity: 0.3;
        transform: scale(1) rotate(0deg);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.1) rotate(180deg);
    }
}

@keyframes patternMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(80px, 80px);
    }
}

@keyframes shapeFloat {

    0%,
    100% {
        transform: translate(0, 0) rotate(0deg);
    }

    25% {
        transform: translate(30px, -30px) rotate(90deg);
    }

    50% {
        transform: translate(-20px, -40px) rotate(180deg);
    }

    75% {
        transform: translate(-30px, -20px) rotate(270deg);
    }
}

@keyframes badgeGlow {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 10px 40px rgba(0, 255, 209, 0.15);
    }

    50% {
        transform: scale(1.05);
        box-shadow: 0 15px 50px rgba(0, 255, 209, 0.25);
    }
}

@keyframes iconSpin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes titleReveal {
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes activityReveal {
    to {
        opacity: 1;
        transform: translateY(0) rotateX(0deg);
    }
}

@keyframes ctaGlow {

    0%,
    100% {
        opacity: 0.3;
    }

    50% {
        opacity: 0.6;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .activities-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
    }

    .activity-card {
        padding: 40px;
    }
}

@media (max-width: 768px) {
    .activities-section-fixed {
        padding: 80px 0;
    }

    .activities-container {
        padding: 0 20px;
    }

    .activities-header {
        margin-bottom: 60px;
    }

    .activities-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        margin: 60px 0;
    }

    .activity-card {
        padding: 30px;
    }

    .activities-cta {
        margin-top: 60px;
        padding: 50px 30px;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-button {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    /* Simplify animations on mobile */
    .shape-1,
    .shape-2 {
        display: none;
    }

    .activity-card:hover {
        transform: translateY(-10px);
    }
}

@media (max-width: 480px) {
    .activity-card {
        padding: 25px;
    }

    .card-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .activity-icon {
        width: 70px;
        height: 70px;
    }

    .activity-icon svg {
        width: 32px;
        height: 32px;
    }

    .activity-title {
        font-size: 1.4rem;
    }

    .activities-cta {
        padding: 40px 20px;
    }
}