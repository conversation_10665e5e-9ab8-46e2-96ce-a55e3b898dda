<?php

/**
 * INOVA Energy Security Functions
 * Professional Security Implementation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Security Headers
 */
function inova_security_headers()
{
    if (!headers_sent()) {
        // Content Security Policy - Fixed for Lord Icons
        header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://cdnjs.cloudflare.com https://cdn.lordicon.com https://unpkg.com; style-src \'self\' \'unsafe-inline\' https://fonts.googleapis.com; font-src \'self\' https://fonts.gstatic.com; img-src \'self\' data: https:; connect-src \'self\' https://cdn.lordicon.com;');

        // Security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');

        // Only add HSTS if HTTPS
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
    }
}

/**
 * Input Sanitization
 */
function inova_sanitize_input($input)
{
    if (is_array($input)) {
        return array_map('inova_sanitize_input', $input);
    }

    if (!is_string($input)) {
        return $input;
    }

    // Remove HTML tags and encode special characters
    $input = strip_tags($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    $input = trim($input);

    // Remove potential SQL injection patterns
    $input = preg_replace('/[\'";\\\\]/', '', $input);

    return $input;
}

/**
 * Email Validation
 */
function inova_validate_email($email)
{
    $email = inova_sanitize_input($email);

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return false;
    }

    // Check for common malicious patterns
    $malicious_patterns = array(
        '/script/i',
        '/javascript/i',
        '/vbscript/i',
        '/onload/i',
        '/onerror/i'
    );

    foreach ($malicious_patterns as $pattern) {
        if (preg_match($pattern, $email)) {
            return false;
        }
    }

    return $email;
}

/**
 * Phone Number Validation
 */
function inova_validate_phone($phone)
{
    $phone = inova_sanitize_input($phone);

    // Remove all non-numeric characters except + and -
    $phone = preg_replace('/[^0-9+\-\s()]/', '', $phone);

    // Check length (minimum 10 digits)
    $digits_only = preg_replace('/[^0-9]/', '', $phone);
    if (strlen($digits_only) < 10) {
        return false;
    }

    return $phone;
}

/**
 * Text Validation
 */
function inova_validate_text($text, $max_length = 1000)
{
    $text = inova_sanitize_input($text);

    if (strlen($text) > $max_length) {
        return false;
    }

    // Check for malicious patterns
    $malicious_patterns = array(
        '/<script/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload=/i',
        '/onerror=/i',
        '/eval\(/i',
        '/expression\(/i'
    );

    foreach ($malicious_patterns as $pattern) {
        if (preg_match($pattern, $text)) {
            return false;
        }
    }

    return $text;
}

/**
 * Nonce Functions
 */
function inova_generate_nonce($action = 'inova_form')
{
    if (function_exists('wp_create_nonce')) {
        return wp_create_nonce($action);
    }

    // Fallback nonce generation
    return hash('sha256', $action . session_id() . time());
}

function inova_verify_nonce($nonce, $action = 'inova_form')
{
    if (function_exists('wp_verify_nonce')) {
        return wp_verify_nonce($nonce, $action);
    }

    // Fallback nonce verification
    $expected = hash('sha256', $action . session_id() . time());
    return hash_equals($expected, $nonce);
}

/**
 * Rate Limiting
 */
function inova_rate_limit_check($ip = null, $max_requests = 10, $time_window = 60)
{
    if (!$ip) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    $cache_key = 'inova_rate_limit_' . md5($ip);

    if (function_exists('get_transient')) {
        $requests = get_transient($cache_key);

        if ($requests === false) {
            set_transient($cache_key, 1, $time_window);
            return true;
        }

        if ($requests >= $max_requests) {
            return false;
        }

        set_transient($cache_key, $requests + 1, $time_window);
        return true;
    }

    // Fallback using session
    if (!isset($_SESSION)) {
        session_start();
    }

    $current_time = time();
    $session_key = 'rate_limit_' . md5($ip);

    if (!isset($_SESSION[$session_key])) {
        $_SESSION[$session_key] = array(
            'count' => 1,
            'start_time' => $current_time
        );
        return true;
    }

    $session_data = $_SESSION[$session_key];

    // Reset if time window has passed
    if ($current_time - $session_data['start_time'] > $time_window) {
        $_SESSION[$session_key] = array(
            'count' => 1,
            'start_time' => $current_time
        );
        return true;
    }

    // Check if limit exceeded
    if ($session_data['count'] >= $max_requests) {
        return false;
    }

    // Increment counter
    $_SESSION[$session_key]['count']++;
    return true;
}

/**
 * Security Event Logging
 */
function inova_log_security_event($event, $details = '', $severity = 'INFO')
{
    $log_entry = array(
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'event' => $event,
        'details' => $details,
        'severity' => $severity
    );

    $log_message = 'INOVA Security [' . $severity . ']: ' . json_encode($log_entry);

    if (function_exists('error_log')) {
        error_log($log_message);
    } else {
        // Fallback to file logging
        $log_file = __DIR__ . '/security.log';
        file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
}

/**
 * File Upload Security
 */
function inova_secure_file_upload($file)
{
    if (!is_array($file) || !isset($file['name'], $file['tmp_name'], $file['size'])) {
        inova_log_security_event('Invalid file upload attempt', '', 'WARNING');
        return false;
    }

    // Allowed file types
    $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt');
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($file_extension, $allowed_types)) {
        inova_log_security_event('Disallowed file type upload', $file_extension, 'WARNING');
        return false;
    }

    // Check file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        inova_log_security_event('File size too large', $file['size'], 'WARNING');
        return false;
    }

    // Check MIME type
    if (function_exists('finfo_open')) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        $allowed_mimes = array(
            'image/jpeg',
            'image/png',
            'image/gif',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        );

        if (!in_array($mime_type, $allowed_mimes)) {
            inova_log_security_event('Invalid MIME type', $mime_type, 'WARNING');
            return false;
        }
    }

    // Scan for malicious content
    $file_content = file_get_contents($file['tmp_name']);
    $malicious_patterns = array(
        '/<\?php/i',
        '/<script/i',
        '/eval\(/i',
        '/exec\(/i',
        '/system\(/i',
        '/shell_exec/i'
    );

    foreach ($malicious_patterns as $pattern) {
        if (preg_match($pattern, $file_content)) {
            inova_log_security_event('Malicious content detected in file', $pattern, 'CRITICAL');
            return false;
        }
    }

    return true;
}

/**
 * SQL Injection Prevention
 */
function inova_prevent_sql_injection($input)
{
    if (is_array($input)) {
        return array_map('inova_prevent_sql_injection', $input);
    }

    // Remove SQL injection patterns
    $sql_patterns = array(
        '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)(\s|$)/i',
        '/(\s|^)(or|and)(\s|$)(\d+|\'[^\']*\'|"[^"]*")(\s|$)(=|<|>|like)/i',
        '/(\s|^)(\'|\")(\s|$)(or|and)(\s|$)(\d+|\'[^\']*\'|"[^"]*")/i',
        '/(\s|^)(\'|\")(\s|$)(=|<|>|like)(\s|$)(\d+|\'[^\']*\'|"[^"]*")/i'
    );

    foreach ($sql_patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            inova_log_security_event('SQL injection attempt detected', $input, 'CRITICAL');
            return false;
        }
    }

    return inova_sanitize_input($input);
}

/**
 * XSS Prevention
 */
function inova_prevent_xss($input)
{
    if (is_array($input)) {
        return array_map('inova_prevent_xss', $input);
    }

    // Remove XSS patterns
    $xss_patterns = array(
        '/<script[^>]*>.*?<\/script>/is',
        '/<iframe[^>]*>.*?<\/iframe>/is',
        '/<object[^>]*>.*?<\/object>/is',
        '/<embed[^>]*>/i',
        '/on\w+\s*=/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/data:text\/html/i'
    );

    foreach ($xss_patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            inova_log_security_event('XSS attempt detected', $input, 'CRITICAL');
            $input = preg_replace($pattern, '', $input);
        }
    }

    return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
}

/**
 * CSRF Protection
 */
function inova_csrf_token()
{
    if (!isset($_SESSION)) {
        session_start();
    }

    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }

    return $_SESSION['csrf_token'];
}

function inova_verify_csrf_token($token)
{
    if (!isset($_SESSION)) {
        session_start();
    }

    if (!isset($_SESSION['csrf_token'])) {
        return false;
    }

    return hash_equals($_SESSION['csrf_token'], $token);
}

// WordPress Security Hooks
if (function_exists('add_action')) {
    add_action('init', 'inova_security_headers');
    add_filter('the_generator', function () {
        return '';
    });
    add_filter('xmlrpc_enabled', '__return_false');
    add_filter('login_errors', function () {
        return 'Invalid credentials.';
    });
}
