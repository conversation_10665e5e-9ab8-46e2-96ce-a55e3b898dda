/* Header & Navigation Styles */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.header-container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0.8rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(0, 31, 63, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Logo Styles */
.header-logo {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    text-decoration: none;
}

.logo-mark {
    width: 40px;
    height: 40px;
    position: relative;
}

.logo-mark svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 8px rgba(0, 163, 255, 0.3));
}

.logo-type {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.logo-text {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-light);
    letter-spacing: 0.5px;
}

.logo-tagline {
    font-size: 1rem;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
}

/* Navigation Styles */
.main-nav {
    margin: 0 2rem;
}

.nav-list {
    display: flex;
    gap: 1.5rem;
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-light);
    text-decoration: none;
    font-size: 1rem;
    font-weight: 500;
    padding: 0.5rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    transition: color 0.3s ease;
}

.nav-line {
    width: 0;
    height: 2px;
    background: linear-gradient(to right, var(--primary), var(--accent));
    transition: width 0.3s ease;
    border-radius: 2px;
}

.nav-link:hover .nav-line,
.nav-link.active .nav-line {
    width: 100%;
}

/* Action Buttons */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.lang-switch {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
}

.lang-switch .icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
}

.cta-button {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1.6rem;
    background: linear-gradient(135deg, 
        rgba(var(--primary-rgb), 0.2),
        rgba(var(--accent-rgb), 0.2));
    border-radius: 14px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg,
        rgba(var(--primary-rgb), 0.3),
        rgba(var(--accent-rgb), 0.3));
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-text {
    font-size: 0.95rem;
}

.btn-arrow {
    width: 20px;
    height: 20px;
    fill: currentColor;
    transform: rotate(180deg);
    transition: transform 0.3s ease;
}

.cta-button:hover .btn-arrow {
    transform: rotate(180deg) translateX(-4px);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    gap: 6px;
    padding: 0.8rem;
}

.menu-line {
    width: 24px;
    height: 2px;
    background-color: var(--text-light);
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .header-container {
        padding: 0.6rem 1.5rem;
    }

    .main-nav {
        margin: 0 1rem;
    }

    .nav-list {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }

    .menu-toggle {
        display: flex;
    }

    .logo-text {
        font-size: 1.2rem;
    }

    .logo-mark {
        width: 36px;
        height: 36px;
    }

    .cta-button {
        padding: 0.6rem 1.2rem;
    }
}

@media (max-width: 480px) {
    .header-container {
        padding: 0.5rem 1rem;
    }

    .logo-tagline {
        display: none;
    }

    .lang-switch {
        width: 36px;
        height: 36px;
    }
}

/* Scroll Animation */
.header-scrolled {
    padding: 0.5rem 0;
}

.header-scrolled .header-container {
    background: rgba(0, 31, 63, 0.95);
    padding: 0.6rem 1.5rem;
}

