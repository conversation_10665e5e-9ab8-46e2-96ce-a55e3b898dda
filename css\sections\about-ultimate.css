/* ===================================
   INOVA ENERGY ABOUT SECTION - ULTIMATE
   Professional & Perfect Design
   =================================== */

/* About Section Base */
.about-section-ultimate {
    position: relative;
    padding: 80px 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 1) 0%,
            rgba(0, 31, 63, 0.98) 50%,
            rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* Background System */
.about-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient-primary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(0, 163, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 255, 209, 0.06) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(0, 163, 255, 0.03) 70%, transparent 90%);
    animation: gradientShift 20s ease-in-out infinite alternate;
}

@keyframes gradientShift {
    0% {
        opacity: 0.7;
        transform: scale(1) rotate(0deg);
    }

    100% {
        opacity: 1;
        transform: scale(1.02) rotate(1deg);
    }
}

.bg-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.particle-field {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 163, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0, 255, 209, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 163, 255, 0.4), transparent);
    background-repeat: repeat;
    background-size: 150px 150px;
    animation: particleDrift 25s linear infinite;
    opacity: 0.4;
}

@keyframes particleDrift {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-150px, -150px);
    }
}

.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.shape-element {
    position: absolute;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.06),
            rgba(0, 255, 209, 0.04));
    border-radius: 50%;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.03);
    animation: shapeFloat 15s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 8%;
    animation-delay: -5s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    top: 30%;
    left: 80%;
    animation-delay: -10s;
}

@keyframes shapeFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.3;
    }

    25% {
        transform: translateY(-40px) rotate(90deg);
        opacity: 0.6;
    }

    50% {
        transform: translateY(-80px) rotate(180deg);
        opacity: 0.4;
    }

    75% {
        transform: translateY(-40px) rotate(270deg);
        opacity: 0.7;
    }
}

/* About Container */
.about-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.about-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 24px;
    font-size: 0.9rem;
    color: var(--accent);
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.header-badge:hover {
    background: rgba(0, 163, 255, 0.15);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-2px);
}

.badge-icon {
    font-size: 1.1rem;
}

.section-title {
    margin-bottom: 24px;
}

.title-main {
    display: block;
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--text-light);
    line-height: 1.1;
    margin-bottom: 8px;
}

.title-sub {
    display: block;
    font-size: 2rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.section-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
    font-weight: 400;
}

/* Content Grid */
.about-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-bottom: 80px;
}

/* Story Section */
.story-section {
    position: relative;
}

.story-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    height: 100%;
}

.story-card:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 163, 255, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.1);
}

.story-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 30px;
}

.story-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 163, 255, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.story-card:hover .story-icon {
    background: rgba(0, 163, 255, 0.2);
    transform: scale(1.1);
}

.story-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin: 0;
}

.story-timeline {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 30px;
    top: 50px;
    width: 2px;
    height: 40px;
    background: linear-gradient(180deg, var(--primary), transparent);
}

.timeline-year {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 50%;
    font-weight: 700;
    font-size: 0.9rem;
    color: white;
    position: relative;
    z-index: 2;
}

.timeline-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 8px;
}

.timeline-content p {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
    margin: 0;
}

/* Values Section */
.values-section {
    position: relative;
}

.values-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    height: 100%;
}

.values-card:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 255, 209, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 255, 209, 0.1);
}

.values-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 30px;
}

.values-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 209, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.values-card:hover .values-icon {
    background: rgba(0, 255, 209, 0.2);
    transform: scale(1.1);
}

.values-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin: 0;
}

.values-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.value-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.value-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
}

.value-icon {
    flex-shrink: 0;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 163, 255, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.value-item:hover .value-icon {
    background: rgba(0, 163, 255, 0.2);
    transform: scale(1.1);
}

.value-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 6px;
}

.value-content p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
    margin: 0;
}

.mission-statement {
    margin-top: 20px;
    padding: 20px;
    background: rgba(0, 163, 255, 0.05);
    border: 1px solid rgba(0, 163, 255, 0.1);
    border-radius: 12px;
    text-align: center;
}

.mission-quote p {
    font-size: 1.1rem;
    font-style: italic;
    color: var(--accent);
    margin: 0;
    font-weight: 500;
    line-height: 1.5;
}

/* Statistics Section */
.about-statistics {
    margin-bottom: 80px;
}

.stats-header {
    text-align: center;
    margin-bottom: 50px;
}

.stats-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 163, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.stat-item:hover::before {
    left: 100%;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 163, 255, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 163, 255, 0.1);
    border-radius: 15px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-icon {
    background: rgba(0, 163, 255, 0.2);
    transform: scale(1.1) rotate(5deg);
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 4px;
    transition: all 0.3s ease;
}

.stat-plus {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent);
    margin-left: 4px;
}

.stat-label {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 8px;
}

/* Vision Section */
.about-vision {
    position: relative;
}

.vision-card {
    position: relative;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 25px;
    padding: 50px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    overflow: hidden;
}

.vision-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.vision-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    margin: -150px 0 0 -150px;
    background: radial-gradient(circle,
            rgba(0, 163, 255, 0.1) 0%,
            rgba(0, 255, 209, 0.05) 50%,
            transparent 70%);
    border-radius: 50%;
    animation: visionPulse 4s ease-in-out infinite alternate;
}

@keyframes visionPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

.vision-card:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 163, 255, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 163, 255, 0.15);
}

.vision-content {
    position: relative;
    z-index: 2;
}

.vision-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 30px;
}

.vision-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 163, 255, 0.1);
    border-radius: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.vision-card:hover .vision-icon {
    background: rgba(0, 163, 255, 0.2);
    transform: scale(1.1) rotate(5deg);
}

.vision-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin: 0;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.vision-text {
    text-align: center;
    margin-bottom: 40px;
}

.vision-text p {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-weight: 400;
}

.vision-goals {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.goal-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px 15px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.goal-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 163, 255, 0.2);
    transform: translateY(-5px);
}

.goal-icon {
    font-size: 2rem;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.goal-item:hover .goal-icon {
    transform: scale(1.2);
}

.goal-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.3;
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Tablet Landscape (1024px - 1399px) */
@media (max-width: 1399px) and (min-width: 1024px) {
    .about-container {
        padding: 0 30px;
    }

    .about-content-grid {
        gap: 40px;
    }

    .title-main {
        font-size: 3rem;
    }

    .title-sub {
        font-size: 1.8rem;
    }

    .stats-grid {
        gap: 25px;
    }

    .vision-goals {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

/* Tablet Portrait (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .about-section-ultimate {
        padding: 80px 0;
    }

    .about-container {
        padding: 0 25px;
    }

    .about-header {
        margin-bottom: 60px;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .about-content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
        margin-bottom: 60px;
    }

    .story-card,
    .values-card {
        padding: 30px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .vision-card {
        padding: 40px 30px;
    }

    .vision-goals {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

/* Mobile Large (481px - 767px) */
@media (max-width: 767px) {
    .about-section-ultimate {
        padding: 60px 0;
        overflow-x: hidden;
    }

    .about-container {
        padding: 0 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .about-header {
        margin-bottom: 50px;
        text-align: center;
    }

    .header-badge {
        padding: 6px 16px;
        font-size: 0.85rem;
        margin: 0 auto 20px;
        display: inline-block;
    }

    .title-main {
        font-size: 2.2rem;
        text-align: center;
        margin-bottom: 10px;
    }

    .title-sub {
        font-size: 1.3rem;
        text-align: center;
        margin-bottom: 20px;
    }

    .section-description {
        font-size: 1rem;
        max-width: 100%;
        text-align: center;
        line-height: 1.6;
    }

    .about-content-grid {
        margin-bottom: 50px;
        grid-template-columns: 1fr;
        gap: 30px;
        width: 100%;
    }

    .story-card,
    .values-card {
        padding: 25px;
        width: 100%;
        box-sizing: border-box;
        margin: 0;
    }

    .story-header,
    .values-header {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        align-items: center;
    }

    .story-icon,
    .values-icon {
        width: 50px;
        height: 50px;
    }

    .story-title,
    .values-title {
        font-size: 1.3rem;
    }

    .timeline-item {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .timeline-item:not(:last-child)::after {
        display: none;
    }

    .timeline-year {
        width: 50px;
        height: 50px;
        font-size: 0.8rem;
    }

    .value-item {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        padding: 20px 16px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .stat-item {
        padding: 25px 15px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-plus {
        font-size: 1.2rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }

    .vision-card {
        padding: 30px 20px;
    }

    .vision-icon {
        width: 60px;
        height: 60px;
    }

    .vision-title {
        font-size: 1.6rem;
    }

    .vision-text p {
        font-size: 1rem;
    }

    .vision-goals {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .goal-item {
        flex-direction: row;
        text-align: left;
        gap: 16px;
        padding: 16px;
    }

    .goal-icon {
        font-size: 1.5rem;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .goal-text {
        font-size: 0.9rem;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .about-section-ultimate {
        padding: 50px 0;
    }

    .about-container {
        padding: 0 15px;
    }

    .about-header {
        margin-bottom: 40px;
    }

    .header-badge {
        padding: 5px 12px;
        font-size: 0.8rem;
    }

    .title-main {
        font-size: 1.9rem;
        line-height: 1.2;
    }

    .title-sub {
        font-size: 1.1rem;
    }

    .section-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .about-content-grid {
        gap: 30px;
        margin-bottom: 40px;
    }

    .story-card,
    .values-card {
        padding: 20px;
    }

    .story-icon,
    .values-icon {
        width: 44px;
        height: 44px;
    }

    .story-title,
    .values-title {
        font-size: 1.2rem;
    }

    .timeline-year {
        width: 44px;
        height: 44px;
        font-size: 0.75rem;
    }

    .timeline-content h4 {
        font-size: 1rem;
    }

    .timeline-content p {
        font-size: 0.85rem;
    }

    .value-item {
        padding: 16px 12px;
    }

    .value-icon {
        width: 36px;
        height: 36px;
    }

    .value-content h4 {
        font-size: 0.95rem;
    }

    .value-content p {
        font-size: 0.85rem;
    }

    .mission-statement {
        padding: 16px;
    }

    .mission-quote p {
        font-size: 1rem;
    }

    .about-statistics {
        margin-bottom: 40px;
    }

    .stats-header {
        margin-bottom: 30px;
    }

    .stats-title {
        font-size: 2rem;
    }

    .stats-subtitle {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .stat-item {
        padding: 20px 15px;
    }

    .stat-icon {
        width: 44px;
        height: 44px;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-plus {
        font-size: 1rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .vision-card {
        padding: 25px 15px;
    }

    .vision-icon {
        width: 50px;
        height: 50px;
    }

    .vision-title {
        font-size: 1.4rem;
    }

    .vision-text p {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .goal-item {
        padding: 12px;
        gap: 12px;
    }

    .goal-icon {
        font-size: 1.3rem;
    }

    .goal-text {
        font-size: 0.85rem;
    }

    /* Reduce animations for better performance */
    .shape-element,
    .vision-glow {
        animation-duration: 8s;
    }

    .particle-field {
        animation-duration: 15s;
    }
}

/* RTL Support */
.rtl .about-content-grid {
    direction: rtl;
}

.rtl .story-header,
.rtl .values-header {
    flex-direction: row-reverse;
}

.rtl .timeline-item {
    flex-direction: row-reverse;
}

.rtl .value-item {
    flex-direction: row-reverse;
}

@media (max-width: 767px) {

    .rtl .story-header,
    .rtl .values-header {
        flex-direction: column;
    }

    .rtl .timeline-item {
        flex-direction: column;
    }

    .rtl .value-item {
        flex-direction: column;
    }

    .rtl .goal-item {
        flex-direction: row-reverse;
        text-align: right;
    }
}

@media (max-width: 480px) {
    .rtl .goal-item {
        flex-direction: row;
        text-align: right;
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .about-section-ultimate *,
    .about-section-ultimate *::before,
    .about-section-ultimate *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .about-section-ultimate {
        background: #000;
    }

    .header-badge,
    .story-card,
    .values-card,
    .stat-item,
    .vision-card {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .section-description,
    .timeline-content p,
    .value-content p,
    .stats-subtitle,
    .stat-label,
    .vision-text p {
        color: #fff;
    }
}