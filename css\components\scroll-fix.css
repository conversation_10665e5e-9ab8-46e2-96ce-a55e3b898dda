/* ===================================
   SCROLL FIX
   Complete scroll behavior optimization
   =================================== */

/* Global Scroll Reset */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
    width: 100%;
    height: 100%;
}

body {
    overflow-x: hidden;
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    position: relative;
}

/* Container Width Control */
.hero-container-professional,
.about-container,
.services-container-professional,
.activities-container,
.rebranding-container,
.contact-container {
    max-width: 1400px;
    width: 100%;
    margin: 0 auto;
    padding: 0 40px;
    box-sizing: border-box;
}

/* Section Width Control */
.hero-section-professional,
.about-section-ultimate,
.services-section-professional,
.activities-section-fixed,
.rebranding-section-ultimate,
.contact-section-fixed,
.projects-section {
    width: 100%;
    overflow: hidden;
    position: relative;
}

/* Grid Overflow Fix */
.services-grid-professional,
.activities-grid,
.about-content-grid,
.rebranding-content-grid,
.contact-content {
    width: 100%;
    box-sizing: border-box;
}

/* Card Overflow Fix */
.service-card-professional,
.activity-card,
.story-card,
.values-card,
.info-card,
.form-card {
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* Background Elements Fix */
.hero-background,
.about-background,
.services-background-system,
.activities-background,
.rebranding-background-system,
.contact-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

/* Floating Elements Control */
.floating-elements,
.floating-shapes-advanced,
.energy-particles-advanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

/* Animation Elements Fix */
.floating-element,
.shape-advanced,
.particle-advanced {
    position: absolute;
    will-change: transform;
    contain: layout style paint;
}

/* Header Fix */
.site-header {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-sizing: border-box;
}

.header-container {
    max-width: 1400px;
    width: 100%;
    margin: 0 auto;
    padding: 0 40px;
    box-sizing: border-box;
}

/* Navigation Fix */
.nav-menu,
.mobile-nav-menu {
    width: 100%;
    box-sizing: border-box;
}

/* Form Elements Fix */
.contact-form {
    width: 100%;
    box-sizing: border-box;
}

.form-grid {
    width: 100%;
    box-sizing: border-box;
}

.form-group input,
.form-group textarea {
    width: 100%;
    box-sizing: border-box;
}

/* Button Fix */
.cta-primary,
.cta-secondary,
.cta-primary-advanced,
.cta-secondary-advanced,
.cta-button-professional,
.service-cta-professional,
.submit-btn {
    box-sizing: border-box;
    white-space: nowrap;
}

/* Text Overflow Fix */
.hero-title-professional,
.section-title,
.section-title-professional,
.section-title-ultimate,
.activity-title,
.service-title-professional {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Image Fix */
img {
    max-width: 100%;
    height: auto;
    box-sizing: border-box;
}

/* Prevent Horizontal Scroll */
.site-main {
    overflow-x: hidden;
    width: 100%;
}

/* Mobile Specific Fixes */
@media (max-width: 768px) {
    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .activities-container,
    .rebranding-container,
    .contact-container {
        padding: 0 20px;
    }
    
    .header-container {
        padding: 0 20px;
    }
    
    /* Grid Mobile Fix */
    .services-grid-professional,
    .activities-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .about-content-grid,
    .rebranding-content-grid,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    /* Card Mobile Fix */
    .service-card-professional,
    .activity-card,
    .story-card,
    .values-card,
    .info-card,
    .form-card {
        padding: 30px;
    }
}

@media (max-width: 480px) {
    .hero-container-professional,
    .about-container,
    .services-container-professional,
    .activities-container,
    .rebranding-container,
    .contact-container {
        padding: 0 15px;
    }
    
    .header-container {
        padding: 0 15px;
    }
    
    .service-card-professional,
    .activity-card,
    .story-card,
    .values-card,
    .info-card,
    .form-card {
        padding: 20px;
    }
}

/* Scroll Snap Fix */
html {
    scroll-snap-type: none;
}

/* Smooth Scroll Enhancement */
@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
    }
}

/* Performance Optimization */
.hero-section-professional,
.about-section-ultimate,
.services-section-professional,
.activities-section-fixed,
.rebranding-section-ultimate,
.contact-section-fixed {
    contain: layout style paint;
}

/* Z-index Management */
.site-header {
    z-index: 1000;
}

.hero-background,
.about-background,
.services-background-system,
.activities-background,
.rebranding-background-system,
.contact-background {
    z-index: 1;
}

.hero-container-professional,
.about-container,
.services-container-professional,
.activities-container,
.rebranding-container,
.contact-container {
    z-index: 10;
    position: relative;
}

/* Fix for Webkit Scroll Issues */
body {
    -webkit-overflow-scrolling: touch;
}

/* Prevent Bounce Scroll on iOS */
body {
    overscroll-behavior: none;
}

/* Fix for Edge Cases */
.site-main > * {
    max-width: 100%;
    box-sizing: border-box;
}

/* Animation Performance */
.floating-element,
.shape-advanced,
.particle-advanced {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Critical Layout Fix */
.site-main {
    position: relative;
    z-index: 1;
    background: transparent;
}
