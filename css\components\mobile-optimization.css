/* ===================================
   INOVA ENERGY MOBILE OPTIMIZATION
   Advanced Mobile Experience Enhancement
   =================================== */

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
    /* Reduce animations for better performance */
    * {
        animation-duration: 0.3s !important;
        transition-duration: 0.3s !important;
    }
    
    /* Optimize backdrop filters */
    .neo-glass,
    .glass-effect,
    .hero-content-wrapper,
    .service-card,
    .project-card,
    .about-values-modern,
    .about-mission-modern {
        backdrop-filter: blur(10px);
    }
    
    /* Reduce complex gradients */
    .hero-background::before,
    .about-hero-bg,
    .services-bg .gradient-mesh {
        background: linear-gradient(135deg, 
            rgba(0, 31, 63, 0.8) 0%, 
            rgba(0, 15, 30, 0.9) 100%
        );
    }
}

/* Mobile Touch Interactions */
@media (max-width: 768px) {
    /* Enhanced touch targets */
    .touchable {
        min-height: 44px;
        min-width: 44px;
        position: relative;
    }
    
    .touchable::before {
        content: '';
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        z-index: -1;
    }
    
    /* Button touch feedback */
    button, .btn, .action-btn {
        -webkit-tap-highlight-color: rgba(0, 163, 255, 0.3);
        tap-highlight-color: rgba(0, 163, 255, 0.3);
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }
    
    button:active, .btn:active, .action-btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    /* Link touch feedback */
    a {
        -webkit-tap-highlight-color: rgba(0, 163, 255, 0.2);
        tap-highlight-color: rgba(0, 163, 255, 0.2);
    }
    
    /* Card touch feedback */
    .service-card:active,
    .project-card:active,
    .feature-circle:active,
    .stat-card:active {
        transform: scale(0.99);
        transition: transform 0.1s ease;
    }
}

/* Mobile Typography Enhancements */
@media (max-width: 768px) {
    /* Improved readability */
    body {
        font-size: 16px;
        line-height: 1.6;
        -webkit-text-size-adjust: 100%;
        text-size-adjust: 100%;
    }
    
    /* Better text rendering */
    h1, h2, h3, h4, h5, h6 {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    /* Prevent text overflow */
    p, span, div {
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        -webkit-hyphens: auto;
        -moz-hyphens: auto;
        -ms-hyphens: auto;
    }
    
    /* Mobile-optimized line heights */
    .section-title {
        line-height: 1.2;
        margin-bottom: 16px;
    }
    
    .section-subtitle {
        line-height: 1.5;
        margin-bottom: 24px;
    }
    
    .main-description,
    .service-description,
    .project-description {
        line-height: 1.6;
        margin-bottom: 20px;
    }
}

/* Mobile Layout Optimizations */
@media (max-width: 768px) {
    /* Prevent horizontal scroll */
    html, body {
        overflow-x: hidden;
        width: 100%;
    }
    
    /* Mobile-first containers */
    .container,
    .hero-content,
    .services-container,
    .about-container,
    .projects-container,
    .contact-container,
    .footer-container {
        width: 100%;
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
        margin-left: auto;
        margin-right: auto;
    }
    
    /* Mobile grid improvements */
    .hero-grid,
    .about-cards-grid,
    .services-grid,
    .projects-grid,
    .contact-content {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }
    
    /* Mobile section spacing */
    section {
        padding: 50px 0;
        margin: 0;
    }
    
    /* Mobile card layouts */
    .circular-features,
    .values-list,
    .stats-grid,
    .process-steps {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }
    
    /* Mobile statistics layout */
    .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }
}

/* Mobile Form Optimizations */
@media (max-width: 768px) {
    /* Form container */
    .contact-form,
    .form-card {
        padding: 30px 20px;
        border-radius: 16px;
    }
    
    /* Form rows */
    .form-row {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }
    
    /* Form groups */
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-size: 0.95rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
    }
    
    /* Form inputs */
    .form-group input,
    .form-group textarea,
    .form-group select {
        width: 100%;
        min-height: 50px;
        padding: 15px 20px;
        font-size: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        color: var(--text-light);
        transition: all 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus {
        outline: none;
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 0 0 3px rgba(0, 163, 255, 0.1);
    }
    
    .form-group textarea {
        min-height: 120px;
        resize: vertical;
    }
    
    /* Submit button */
    .submit-btn {
        width: 100%;
        min-height: 52px;
        padding: 16px 32px;
        font-size: 1.1rem;
        font-weight: 600;
        background: linear-gradient(135deg, var(--primary), var(--accent));
        border: none;
        border-radius: 26px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 20px;
    }
    
    .submit-btn:hover,
    .submit-btn:focus {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 163, 255, 0.3);
    }
    
    .submit-btn:active {
        transform: translateY(0);
    }
}

/* Mobile Navigation Enhancements */
@media (max-width: 768px) {
    /* Mobile header */
    .main-header {
        padding: 15px 0;
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .header-container {
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    /* Mobile logo */
    .header-logo {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .header-logo .logo-mark {
        width: 35px;
        height: 35px;
    }
    
    .header-logo .logo-text {
        font-size: 1.3rem;
    }
    
    .header-logo .logo-tagline {
        font-size: 0.7rem;
        letter-spacing: 1px;
    }
    
    /* Mobile navigation */
    .main-nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100vh;
        background: rgba(0, 31, 63, 0.95);
        backdrop-filter: blur(20px);
        padding: 80px 30px 30px;
        transition: left 0.3s ease;
        z-index: 1000;
        overflow-y: auto;
    }
    
    .main-nav.active {
        left: 0;
    }
    
    .nav-list {
        display: flex;
        flex-direction: column;
        gap: 0;
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .nav-item {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .nav-link {
        display: block;
        padding: 18px 0;
        font-size: 1.1rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .nav-link:hover,
    .nav-link.active {
        color: var(--accent);
        padding-left: 10px;
    }
    
    /* Mobile menu toggle */
    .menu-toggle {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
        background: transparent;
        border: none;
        cursor: pointer;
        z-index: 1001;
    }
    
    .menu-line {
        width: 25px;
        height: 2px;
        background: var(--text-light);
        margin: 3px 0;
        transition: all 0.3s ease;
        border-radius: 1px;
    }
    
    .menu-toggle.active .menu-line:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }
    
    .menu-toggle.active .menu-line:nth-child(2) {
        opacity: 0;
    }
    
    .menu-toggle.active .menu-line:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }
    
    /* Mobile overlay */
    .nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
    }
    
    .nav-overlay.active {
        opacity: 1;
        visibility: visible;
    }
}

/* Mobile Footer Optimizations */
@media (max-width: 768px) {
    .main-footer {
        padding: 50px 0 0;
        margin-top: 50px;
    }
    
    .footer-container {
        padding: 0 20px;
    }
    
    .footer-content {
        display: flex;
        flex-direction: column;
        gap: 40px;
        text-align: center;
        margin-bottom: 40px;
    }
    
    .footer-logo {
        order: 1;
    }
    
    .footer-nav {
        order: 2;
    }
    
    .footer-contact {
        order: 3;
    }
    
    .footer-social {
        order: 4;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 20px;
        padding: 24px 0;
        text-align: center;
    }
    
    .footer-links {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
}

/* Mobile Performance & Accessibility */
@media (max-width: 768px) {
    /* Reduce motion for better performance */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
    
    /* High contrast support */
    @media (prefers-contrast: high) {
        .service-card,
        .project-card,
        .feature-circle,
        .stat-card {
            border: 2px solid var(--primary);
            background: rgba(255, 255, 255, 0.1);
        }
        
        .main-description,
        .service-description,
        .project-description {
            color: #fff;
        }
    }
    
    /* Focus improvements */
    button:focus,
    .btn:focus,
    .action-btn:focus,
    .nav-link:focus,
    input:focus,
    textarea:focus,
    select:focus {
        outline: 3px solid var(--accent);
        outline-offset: 2px;
    }
    
    /* Skip link for accessibility */
    .skip-link:focus {
        position: fixed;
        top: 10px;
        left: 10px;
        background: var(--primary);
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        text-decoration: none;
        z-index: 10000;
    }
}
