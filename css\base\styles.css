/* Base Styles & Variables */
:root {
    --primary: #00A3FF;
    --primary-rgb: 0, 163, 255;
    --accent: #00FFD1;
    --accent-rgb: 0, 255, 209;
    --secondary: #001F3F;
    --text-light: #FFFFFF;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --transition: all 0.3s ease;
}

/* Reset & Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Vazirmatn', sans-serif;
    line-height: 1.6;
    background: var(--secondary);
    color: var(--text-light);
    overflow-x: hidden;
}

/* Common Utility Classes */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
}

.neo-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}