/* ===================================
   INOVA ENERGY REBRANDING SECTION - FIXED
   Professional & Organized Design
   =================================== */

/* Rebranding Section Base */
.rebranding-section-fixed {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg,
            rgba(0, 31, 63, 1) 0%,
            rgba(0, 15, 30, 0.98) 50%,
            rgba(0, 31, 63, 1) 100%);
    overflow: hidden;
}

/* Background */
.rebranding-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 163, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(0, 255, 209, 0.06) 0%, transparent 50%);
    animation: gradientShift 20s ease-in-out infinite alternate;
}

@keyframes gradientShift {
    0% {
        opacity: 0.6;
        transform: scale(1) rotate(0deg);
    }

    100% {
        opacity: 1;
        transform: scale(1.02) rotate(1deg);
    }
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 50px 80px, rgba(0, 163, 255, 0.15), transparent),
        radial-gradient(1px 1px at 100px 160px, rgba(0, 255, 209, 0.1), transparent);
    background-size: 150px 150px;
    animation: patternMove 25s linear infinite;
    opacity: 0.4;
}

@keyframes patternMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-150px, -150px);
    }
}

.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape-1,
.shape-2 {
    position: absolute;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.04),
            rgba(0, 255, 209, 0.02));
    border-radius: 50%;
    backdrop-filter: blur(20px);
    animation: shapeFloat 25s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 15%;
    left: -5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 70%;
    right: -3%;
    animation-delay: -12s;
}

@keyframes shapeFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.3;
    }

    50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 0.6;
    }
}

/* Rebranding Container */
.rebranding-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.rebranding-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 24px;
    font-size: 0.9rem;
    color: var(--accent);
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.1rem;
}

.section-title {
    margin-bottom: 24px;
}

.title-main {
    display: block;
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-light);
    line-height: 1.1;
    margin-bottom: 8px;
}

.title-sub {
    display: block;
    font-size: 1.8rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.section-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
}

/* Comparison Section */
.comparison-section {
    margin-bottom: 80px;
}

.comparison-header {
    text-align: center;
    margin-bottom: 50px;
}

.comparison-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.comparison-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.comparison-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 40px;
    align-items: center;
}

.comparison-item {
    position: relative;
}

.comparison-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    height: 100%;
}

.comparison-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.15);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
}

.card-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 15px;
    transition: all 0.3s ease;
}

.comparison-card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
}

.card-icon svg {
    width: 24px;
    height: 24px;
    color: white;
    stroke-width: 2;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-light);
    margin: 0;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feature-list li:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
}

.feature-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.feature-text {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

/* Before Item Styling */
.before-item .card-icon {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.before-item .feature-icon {
    opacity: 0.7;
}

/* After Item Styling */
.after-item .card-icon {
    background: linear-gradient(135deg, var(--primary), var(--accent));
}

.after-item .feature-icon {
    opacity: 1;
}

/* Comparison Arrow */
.comparison-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.arrow-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(20px);
    animation: arrowPulse 3s ease-in-out infinite;
}

@keyframes arrowPulse {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 163, 255, 0.4);
    }

    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(0, 163, 255, 0);
    }
}

.arrow-line {
    position: absolute;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 1px;
}

.arrow-head {
    position: relative;
    z-index: 2;
}

.arrow-head svg {
    width: 24px;
    height: 24px;
    color: var(--accent);
    stroke-width: 2;
}

/* Key Changes Section */
.key-changes-section {
    margin-bottom: 80px;
}

.changes-header {
    text-align: center;
    margin-bottom: 50px;
}

.changes-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.changes-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.changes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.change-item {
    position: relative;
}

.change-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.change-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.15);
}

.change-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 20px;
    margin-bottom: 24px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.change-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.change-card:hover .change-icon::before {
    left: 100%;
}

.change-card:hover .change-icon {
    transform: scale(1.1) rotate(5deg);
}

.change-icon svg {
    width: 40px;
    height: 40px;
    color: white;
    stroke-width: 2;
    position: relative;
    z-index: 2;
}

.change-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.change-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 12px;
    line-height: 1.3;
}

.change-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Rebranding CTA */
.rebranding-cta {
    text-align: center;
    padding: 60px 40px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 25px;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.rebranding-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center,
            rgba(0, 163, 255, 0.1) 0%,
            transparent 70%);
    animation: ctaPulse 4s ease-in-out infinite alternate;
}

@keyframes ctaPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    text-decoration: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 163, 255, 0.4);
    gap: 16px;
}

.cta-button svg {
    width: 18px;
    height: 18px;
    stroke-width: 2;
    transition: transform 0.3s ease;
}

.cta-button:hover svg {
    transform: translateX(4px);
}

/* ===================================
   RESPONSIVE DESIGN - MOBILE OPTIMIZED
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .rebranding-container {
        padding: 0 30px;
    }

    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .comparison-arrow {
        order: 2;
        transform: rotate(90deg);
        margin: 20px 0;
    }

    .before-item {
        order: 1;
    }

    .after-item {
        order: 3;
    }

    .changes-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .comparison-card,
    .change-card {
        padding: 25px;
    }

    .rebranding-cta {
        padding: 50px 30px;
    }

    .cta-title {
        font-size: 1.8rem;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .rebranding-section-fixed {
        padding: 60px 0;
    }

    .rebranding-container {
        padding: 0 20px;
    }

    .rebranding-header {
        margin-bottom: 50px;
    }

    .header-badge {
        padding: 6px 16px;
        font-size: 0.85rem;
    }

    .title-main {
        font-size: 2rem;
        line-height: 1.2;
    }

    .title-sub {
        font-size: 1.3rem;
    }

    .section-description {
        font-size: 1rem;
        max-width: 100%;
    }

    .comparison-section {
        margin-bottom: 50px;
    }

    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .comparison-arrow {
        order: 2;
        transform: rotate(90deg);
        margin: 15px 0;
        padding: 15px;
    }

    .arrow-container {
        width: 60px;
        height: 60px;
    }

    .arrow-line {
        width: 30px;
    }

    .arrow-head svg {
        width: 20px;
        height: 20px;
    }

    .before-item {
        order: 1;
    }

    .after-item {
        order: 3;
    }

    .comparison-card,
    .change-card {
        padding: 20px;
        border-radius: 15px;
    }

    .card-header {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        margin-bottom: 20px;
    }

    .card-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto;
    }

    .card-icon svg {
        width: 30px;
        height: 30px;
    }

    .card-title {
        font-size: 1.2rem;
        text-align: center;
    }

    .feature-list {
        gap: 12px;
    }

    .feature-list li {
        padding: 10px 12px;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .feature-text {
        font-size: 0.9rem;
    }

    .key-changes-section {
        margin-bottom: 50px;
    }

    .changes-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .change-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
    }

    .change-icon svg {
        width: 35px;
        height: 35px;
    }

    .change-title {
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .change-description {
        font-size: 0.95rem;
    }

    .rebranding-cta {
        padding: 40px 20px;
        border-radius: 20px;
    }

    .cta-title {
        font-size: 1.5rem;
        margin-bottom: 12px;
    }

    .cta-description {
        font-size: 1rem;
        margin-bottom: 24px;
    }

    .cta-button {
        padding: 14px 28px;
        font-size: 0.95rem;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .rebranding-section-fixed {
        padding: 50px 0;
    }

    .rebranding-container {
        padding: 0 15px;
    }

    .rebranding-header {
        margin-bottom: 40px;
    }

    .title-main {
        font-size: 1.8rem;
    }

    .title-sub {
        font-size: 1.1rem;
    }

    .section-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .comparison-section {
        margin-bottom: 40px;
    }

    .comparison-grid {
        gap: 16px;
    }

    .comparison-card,
    .change-card {
        padding: 16px;
    }

    .card-icon {
        width: 50px;
        height: 50px;
    }

    .card-icon svg {
        width: 25px;
        height: 25px;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .feature-list li {
        padding: 8px 10px;
    }

    .feature-text {
        font-size: 0.85rem;
    }

    .key-changes-section {
        margin-bottom: 40px;
    }

    .changes-grid {
        gap: 16px;
    }

    .change-icon {
        width: 60px;
        height: 60px;
    }

    .change-icon svg {
        width: 30px;
        height: 30px;
    }

    .change-title {
        font-size: 1.1rem;
    }

    .change-description {
        font-size: 0.9rem;
    }

    .rebranding-cta {
        padding: 30px 16px;
    }

    .cta-title {
        font-size: 1.3rem;
    }

    .cta-description {
        font-size: 0.95rem;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}

/* RTL Support */
.rtl .comparison-grid {
    direction: rtl;
}

.rtl .feature-list li:hover {
    transform: translateX(-5px);
}

.rtl .cta-button {
    flex-direction: row-reverse;
}

.rtl .cta-button:hover svg {
    transform: translateX(-4px);
}

@media (max-width: 767px) {
    .rtl .feature-list li:hover {
        transform: translateY(-2px);
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .rebranding-section-fixed *,
    .rebranding-section-fixed *::before,
    .rebranding-section-fixed *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .rebranding-section-fixed {
        background: #000;
    }

    .comparison-card,
    .change-card,
    .rebranding-cta {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .feature-text,
    .change-description,
    .cta-description {
        color: #fff;
    }
}