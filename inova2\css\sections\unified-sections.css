/* Unified Styles for Sections */

body {
    margin: 0;
    font-family: 'Vazirmatn', sans-serif;
}

/* Section Styles */
section {
    padding: 60px 20px;
    transition: background-color 0.5s ease, transform 0.5s ease;
}

/* Hero Section */
.hero {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 1)), url('../images/hero-bg.jpg') no-repeat center center/cover;
    color: #333;
}

/* Rebranding Section */
.rebranding {
    background-color: #f9f9f9;
    color: #222;
}

/* Activities Section */
.activities {
    background-color: #fff;
    color: #444;
}

/* About Section */
.about {
    background-color: #f0f0f0;
    color: #555;
}

/* Smooth Scroll Transition */
html {
    scroll-behavior: smooth;
}

/* Section Hover Effects */
section:hover {
    transform: scale(1.02);
}

/* Responsive Styles */
@media (max-width: 768px) {
    section {
        padding: 40px 10px;
    }
}

/* AOS Animation Styles */
[data-aos] {
    opacity: 0;
    transition-property: opacity, transform;
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

[data-aos="fade-up"] {
    transform: translateY(20px);
}

[data-aos="fade-up"].aos-animate {
    transform: translateY(0);
}

[data-aos="fade-down"] {
    transform: translateY(-20px);
}

[data-aos="fade-down"].aos-animate {
    transform: translateY(0);
}