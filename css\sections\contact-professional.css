/* ===================================
   CONTACT SECTION - PROFESSIONAL DESIGN
   Ultra-advanced styling with cutting-edge animations
   =================================== */

/* Contact Section Base */
.contact-section-fixed {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg, 
        rgba(0, 15, 30, 1) 0%, 
        rgba(0, 31, 63, 1) 50%,
        rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* Advanced Background System */
.contact-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(ellipse at 25% 25%, rgba(0, 163, 255, 0.15) 0%, transparent 50%),
        radial-gradient(ellipse at 75% 75%, rgba(0, 255, 209, 0.1) 0%, transparent 50%);
    animation: gradientFlow 12s ease-in-out infinite;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(60deg, rgba(0, 255, 209, 0.03) 1px, transparent 1px),
        linear-gradient(-60deg, rgba(0, 163, 255, 0.03) 1px, transparent 1px);
    background-size: 70px 70px, 50px 50px;
    animation: patternShift 25s linear infinite;
    opacity: 0.4;
}

.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape-1,
.shape-2 {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, 
        rgba(0, 255, 209, 0.06), 
        rgba(0, 163, 255, 0.06));
    animation: shapeFloat 18s ease-in-out infinite;
}

.shape-1 {
    width: 180px;
    height: 180px;
    top: 20%;
    left: 5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    bottom: 25%;
    right: 10%;
    animation-delay: 9s;
}

/* Contact Container */
.contact-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.contact-header {
    text-align: center;
    margin-bottom: 100px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 12px 28px;
    background: rgba(0, 255, 209, 0.1);
    border: 1px solid rgba(0, 255, 209, 0.3);
    border-radius: 50px;
    color: var(--accent);
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 40px;
    backdrop-filter: blur(15px);
    box-shadow: 0 10px 40px rgba(0, 255, 209, 0.15);
    animation: badgePulse 4s ease-in-out infinite;
}

.badge-icon {
    font-size: 1.5rem;
    animation: iconBounce 3s ease-in-out infinite;
}

.title-main {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 15px;
    color: var(--text-light);
    text-shadow: 0 4px 25px rgba(0, 163, 255, 0.4);
    opacity: 0;
    transform: translateY(50px);
    animation: titleReveal 1.5s ease-out 0.5s forwards;
}

.title-sub {
    font-size: clamp(1.5rem, 3vw, 2.2rem);
    font-weight: 700;
    margin-bottom: 30px;
    opacity: 0;
    transform: translateY(50px);
    animation: titleReveal 1.5s ease-out 0.8s forwards;
}

.gradient-text {
    background: linear-gradient(135deg, 
        var(--primary) 0%, 
        var(--accent) 50%,
        #00FFD1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    animation: gradientAnimation 4s ease-in-out infinite;
}

.section-description {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.85);
    max-width: 700px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1.5s ease-out 1.2s forwards;
}

/* Contact Content */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 80px;
    align-items: start;
    margin: 100px 0;
}

/* Info Card */
.info-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 50px;
    backdrop-filter: blur(25px);
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
    opacity: 0;
    transform: translateX(-50px);
    animation: slideInLeft 1.5s ease-out 1.5s forwards;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(0, 255, 209, 0.05) 0%, 
        rgba(0, 163, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.info-card:hover::before {
    opacity: 1;
}

.info-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 209, 0.3);
    box-shadow: 0 25px 80px rgba(0, 163, 255, 0.2);
}

.info-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.info-items {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 40px;
    position: relative;
    z-index: 2;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    transition: all 0.4s ease;
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 255, 209, 0.2);
    transform: translateX(10px);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 8px 25px rgba(0, 163, 255, 0.3);
    transition: all 0.4s ease;
}

.info-item:hover .info-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 35px rgba(0, 163, 255, 0.4);
}

.info-icon svg {
    width: 24px;
    height: 24px;
}

.info-content {
    flex: 1;
}

.info-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 5px;
    font-weight: 500;
}

.info-value {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 600;
}

/* Form Card */
.form-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 50px;
    backdrop-filter: blur(25px);
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
    opacity: 0;
    transform: translateX(50px);
    animation: slideInRight 1.5s ease-out 1.8s forwards;
}

.form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(0, 163, 255, 0.05) 0%, 
        rgba(0, 255, 209, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.form-card:hover::before {
    opacity: 1;
}

.form-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 163, 255, 0.3);
    box-shadow: 0 25px 80px rgba(0, 255, 209, 0.2);
}

.form-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

/* Form Styles */
.contact-form {
    position: relative;
    z-index: 2;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.form-group {
    position: relative;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
    font-weight: 600;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: var(--text-light);
    font-size: 1rem;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 20px rgba(0, 255, 209, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 18px 40px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: var(--text-light);
    border: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.4s ease;
    box-shadow: 0 15px 40px rgba(0, 163, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.2), 
        transparent);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.submit-btn:hover::before {
    opacity: 1;
}

.submit-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 25px 60px rgba(0, 163, 255, 0.4);
}

.btn-icon {
    transition: transform 0.4s ease;
}

.submit-btn:hover .btn-icon {
    transform: translateX(5px);
}

.btn-icon svg {
    width: 20px;
    height: 20px;
}

/* Animations */
@keyframes gradientFlow {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.3; }
    50% { transform: scale(1.1) rotate(180deg); opacity: 0.5; }
}

@keyframes patternShift {
    0% { transform: translate(0, 0); }
    100% { transform: translate(70px, 70px); }
}

@keyframes shapeFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(25px, -25px) rotate(120deg); }
    66% { transform: translate(-15px, -35px) rotate(240deg); }
}

@keyframes badgePulse {
    0%, 100% { 
        transform: scale(1); 
        box-shadow: 0 10px 40px rgba(0, 255, 209, 0.15); 
    }
    50% { 
        transform: scale(1.05); 
        box-shadow: 0 15px 50px rgba(0, 255, 209, 0.25); 
    }
}

@keyframes iconBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes titleReveal {
    to { opacity: 1; transform: translateY(0); }
}

@keyframes gradientAnimation {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
    to { opacity: 1; transform: translateX(0); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .contact-content {
        gap: 60px;
    }
    
    .info-card,
    .form-card {
        padding: 40px;
    }
}

@media (max-width: 768px) {
    .contact-section-fixed {
        padding: 80px 0;
    }
    
    .contact-container {
        padding: 0 20px;
    }
    
    .contact-header {
        margin-bottom: 60px;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 50px;
        margin: 60px 0;
    }
    
    .info-card,
    .form-card {
        padding: 30px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .info-item {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }
    
    .submit-btn {
        width: 100%;
        justify-content: center;
    }
    
    /* Hide complex animations on mobile */
    .shape-1,
    .shape-2 {
        display: none;
    }
}

@media (max-width: 480px) {
    .info-card,
    .form-card {
        padding: 25px;
    }
    
    .info-title,
    .form-title {
        font-size: 1.6rem;
    }
    
    .info-icon {
        width: 45px;
        height: 45px;
    }
    
    .info-icon svg {
        width: 20px;
        height: 20px;
    }
    
    .form-group input,
    .form-group textarea {
        padding: 12px 15px;
    }
}
