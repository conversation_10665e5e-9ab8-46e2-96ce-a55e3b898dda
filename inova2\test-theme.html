<!DOCTYPE html>
<html lang="fa" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>INOVA Energy Theme Test | تست قالب</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">

    <!-- Theme Styles -->
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/base/styles.css">
    <link rel="stylesheet" href="css/components/header.css">
    <link rel="stylesheet" href="css/components/multilingual.css">
    <link rel="stylesheet" href="css/components/english-layout.css">
    <link rel="stylesheet" href="css/components/responsive-master.css">
    <link rel="stylesheet" href="css/components/mobile-optimization.css">
    <link rel="stylesheet" href="css/sections/hero-modern.css">
    <link rel="stylesheet" href="css/sections/about-modern.css">
    <link rel="stylesheet" href="css/sections/services-modern.css">
    <link rel="stylesheet" href="css/sections/footer.css">

    <!-- Third-party Libraries -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">

    <style>
        /* Test Styles */
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-section {
            margin: 40px 0;
            padding: 20px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }

        .test-title {
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .language-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .demo-card {
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .demo-card h4 {
            color: var(--accent);
            margin-bottom: 10px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background: #00ff88;
        }

        .status-warning {
            background: #ffaa00;
        }

        .status-error {
            background: #ff4444;
        }

        .test-button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            background: var(--accent);
            transform: translateY(-2px);
        }

        .rtl .demo-card {
            text-align: right;
        }

        .ltr .demo-card {
            text-align: left;
        }
    </style>
</head>

<body class="lang-fa rtl">
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 class="gradient-text">INOVA Energy WordPress Theme Test</h1>
            <p>تست عملکرد قالب چندزبانه | Multilingual Theme Test</p>

            <!-- Language Switch Button -->
            <button class="lang-switch test-button" onclick="testLanguageSwitch()">
                <svg viewBox="0 0 24 24" class="icon" style="width: 16px; height: 16px; fill: currentColor;">
                    <path
                        d="M12.87,15.07L10.33,12.56L10.36,12.53C12.1,10.59 13.34,8.36 14.07,6H17V4H10V2H8V4H1V6H12.17C11.5,7.92 10.44,9.75 9,11.35C8.07,10.32 7.3,9.19 6.69,8H4.69C5.42,9.63 6.42,11.17 7.67,12.56L2.58,17.58L4,19L9,14L12.11,17.11L12.87,15.07M18.5,10H16.5L12,22H14L15.12,19H19.87L21,22H23L18.5,10M15.88,17L17.5,12.67L19.12,17H15.88Z" />
                </svg>
                <span class="lang-text">EN</span>
            </button>
        </header>

        <!-- Language Test Section -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-success"></span>
                Language System Test | تست سیستم زبان
            </h2>

            <div class="language-demo">
                <div class="demo-card">
                    <h4>Persian (فارسی)</h4>
                    <p>این متن به زبان فارسی نوشته شده است و باید از راست به چپ نمایش داده شود.</p>
                    <p><strong>فونت:</strong> Vazirmatn</p>
                    <p><strong>جهت:</strong> راست به چپ (RTL)</p>
                </div>

                <div class="demo-card">
                    <h4>English</h4>
                    <p>This text is written in English and should be displayed from left to right.</p>
                    <p><strong>Font:</strong> Inter</p>
                    <p><strong>Direction:</strong> Left to Right (LTR)</p>
                </div>
            </div>
        </div>

        <!-- Typography Test -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-success"></span>
                Typography Test | تست تایپوگرافی
            </h2>

            <h1>Heading 1 | عنوان ۱</h1>
            <h2>Heading 2 | عنوان ۲</h2>
            <h3>Heading 3 | عنوان ۳</h3>
            <h4>Heading 4 | عنوان ۴</h4>
            <p>Regular paragraph text | متن پاراگراف معمولی</p>
            <p><strong>Bold text | متن پررنگ</strong></p>
            <p><em>Italic text | متن کج</em></p>
        </div>

        <!-- Component Test -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-success"></span>
                Component Test | تست کامپوننت‌ها
            </h2>

            <div style="display: flex; gap: 20px; flex-wrap: wrap; justify-content: center;">
                <button class="test-button">Button | دکمه</button>
                <button class="test-button" style="background: var(--accent);">Accent Button | دکمه تاکیدی</button>
                <button class="test-button" style="background: transparent; border: 1px solid var(--primary);">Outline
                    Button | دکمه خالی</button>
            </div>
        </div>

        <!-- Animation Test -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-warning"></span>
                Animation Test | تست انیمیشن
            </h2>

            <p>Animations require full WordPress environment with JavaScript libraries.</p>
            <p>انیمیشن‌ها نیاز به محیط کامل وردپرس با کتابخانه‌های جاوااسکریپت دارند.</p>

            <div class="demo-card" data-aos="fade-up">
                <h4>AOS Animation Test</h4>
                <p>This card should animate when scrolling (requires AOS library).</p>
                <p>این کارت باید هنگام اسکرول انیمیت شود (نیاز به کتابخانه AOS).</p>
            </div>
        </div>

        <!-- Responsive Test -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-success"></span>
                Responsive Test | تست ریسپانسیو
            </h2>

            <p>Resize your browser window to test responsive behavior.</p>
            <p>برای تست رفتار ریسپانسیو، اندازه پنجره مرورگر را تغییر دهید.</p>

            <div
                style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 20px;">
                <div class="demo-card">Card 1 | کارت ۱</div>
                <div class="demo-card">Card 2 | کارت ۲</div>
                <div class="demo-card">Card 3 | کارت ۳</div>
            </div>
        </div>

        <!-- Status Summary -->
        <div class="test-section">
            <h2 class="test-title">Test Summary | خلاصه تست</h2>

            <ul style="list-style: none; padding: 0;">
                <li><span class="status-indicator status-success"></span> Language switching system</li>
                <li><span class="status-indicator status-success"></span> RTL/LTR layout support</li>
                <li><span class="status-indicator status-success"></span> Typography and fonts</li>
                <li><span class="status-indicator status-success"></span> Responsive design</li>
                <li><span class="status-indicator status-success"></span> CSS components</li>
                <li><span class="status-indicator status-warning"></span> JavaScript animations (requires WordPress)
                </li>
                <li><span class="status-indicator status-warning"></span> WordPress integration (requires WordPress)
                </li>
            </ul>
        </div>
    </div>

    <!-- Test Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Simple language switch test
        function testLanguageSwitch() {
            const body = document.body;
            const html = document.documentElement;
            const langText = document.querySelector('.lang-text');

            if (body.classList.contains('lang-fa')) {
                // Switch to English
                body.classList.remove('lang-fa', 'rtl');
                body.classList.add('lang-en', 'ltr');
                html.setAttribute('dir', 'ltr');
                html.setAttribute('lang', 'en');
                langText.textContent = 'فا';

                // Update title
                document.title = 'INOVA Energy Theme Test | Multilingual WordPress Theme';
            } else {
                // Switch to Persian
                body.classList.remove('lang-en', 'ltr');
                body.classList.add('lang-fa', 'rtl');
                html.setAttribute('dir', 'rtl');
                html.setAttribute('lang', 'fa');
                langText.textContent = 'EN';

                // Update title
                document.title = 'تست قالب INOVA Energy | قالب وردپرس چندزبانه';
            }

            console.log('Language switched to:', body.classList.contains('lang-fa') ? 'Persian' : 'English');
        }

        // Test console output
        console.log('INOVA Energy Theme Test Loaded');
        console.log('Current language:', document.body.classList.contains('lang-fa') ? 'Persian (RTL)' : 'English (LTR)');
    </script>
</body>

</html>