/* ===================================
   INOVA ENERGY SERVICES SECTION - FIXED
   Professional & Clean Design
   =================================== */

/* Services Section Base */
.services-section-fixed {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg,
            rgba(0, 15, 30, 1) 0%,
            rgba(0, 31, 63, 0.95) 50%,
            rgba(0, 15, 30, 1) 100%);
    overflow: hidden;
}

/* Background */
.services-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 163, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(0, 255, 209, 0.06) 0%, transparent 50%);
    animation: gradientMove 15s ease-in-out infinite alternate;
}

@keyframes gradientMove {
    0% {
        opacity: 0.6;
        transform: scale(1);
    }

    100% {
        opacity: 1;
        transform: scale(1.05);
    }
}

.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape-1,
.shape-2 {
    position: absolute;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.05),
            rgba(0, 255, 209, 0.03));
    border-radius: 50%;
    backdrop-filter: blur(20px);
    animation: shapeFloat 20s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: -5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 70%;
    right: -5%;
    animation-delay: -10s;
}

@keyframes shapeFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.3;
    }

    50% {
        transform: translateY(-50px) rotate(180deg);
        opacity: 0.6;
    }
}

/* Services Container */
.services-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Section Header */
.services-header {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 24px;
    font-size: 0.9rem;
    color: var(--accent);
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.1rem;
}

.section-title {
    margin-bottom: 24px;
}

.title-main {
    display: block;
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-light);
    line-height: 1.1;
    margin-bottom: 8px;
}

.title-sub {
    display: block;
    font-size: 1.8rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.section-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 30px;
    margin-bottom: 80px;
}

/* Service Card */
.service-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 40px 30px;
    backdrop-filter: blur(20px);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 163, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.15);
}

/* Service Icon */
.service-icon {
    margin-bottom: 24px;
}

.icon-wrapper {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.icon-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.service-card:hover .icon-wrapper::before {
    transform: translateX(100%);
}

.service-card:hover .icon-wrapper {
    transform: scale(1.1) rotate(5deg);
}

.icon-wrapper svg {
    width: 40px;
    height: 40px;
    color: white;
    stroke-width: 2;
}

/* Service Content */
.service-content {
    position: relative;
    z-index: 2;
}

.service-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 16px;
    line-height: 1.3;
}

.service-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
}

/* Service Features */
.service-features {
    list-style: none;
    padding: 0;
    margin: 0 0 24px 0;
}

.service-features li {
    position: relative;
    padding: 8px 0 8px 24px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 8px;
    color: var(--accent);
    font-weight: bold;
    font-size: 0.9rem;
}

/* Service Link */
.service-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    padding: 8px 0;
}

.service-link:hover {
    color: var(--accent);
    gap: 12px;
}

.service-link svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
    transition: transform 0.3s ease;
}

.service-link:hover svg {
    transform: translateX(4px);
}

/* Call to Action */
.services-cta {
    text-align: center;
    padding: 60px 40px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 25px;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.services-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center,
            rgba(0, 163, 255, 0.1) 0%,
            transparent 70%);
    animation: ctaPulse 4s ease-in-out infinite alternate;
}

@keyframes ctaPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    text-decoration: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 163, 255, 0.4);
    gap: 16px;
}

.cta-button svg {
    width: 18px;
    height: 18px;
    stroke-width: 2;
    transition: transform 0.3s ease;
}

.cta-button:hover svg {
    transform: translateX(4px);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .services-container {
        padding: 0 30px;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .service-card {
        padding: 35px 25px;
    }

    .icon-wrapper {
        width: 70px;
        height: 70px;
    }

    .icon-wrapper svg {
        width: 35px;
        height: 35px;
    }

    .service-title {
        font-size: 1.3rem;
    }

    .services-cta {
        padding: 50px 30px;
    }

    .cta-title {
        font-size: 1.8rem;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 767px) {
    .services-section-fixed {
        padding: 60px 0;
    }

    .services-container {
        padding: 0 20px;
    }

    .services-header {
        margin-bottom: 50px;
    }

    .header-badge {
        padding: 6px 16px;
        font-size: 0.85rem;
    }

    .title-main {
        font-size: 2rem;
        line-height: 1.2;
    }

    .title-sub {
        font-size: 1.3rem;
    }

    .section-description {
        font-size: 1rem;
        max-width: 100%;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 50px;
    }

    .service-card {
        padding: 30px 20px;
        border-radius: 15px;
    }

    .service-icon {
        margin-bottom: 20px;
        text-align: center;
    }

    .icon-wrapper {
        width: 60px;
        height: 60px;
        margin: 0 auto;
        border-radius: 15px;
    }

    .icon-wrapper svg {
        width: 30px;
        height: 30px;
    }

    .service-title {
        font-size: 1.2rem;
        text-align: center;
        margin-bottom: 12px;
    }

    .service-description {
        font-size: 0.95rem;
        text-align: center;
        margin-bottom: 16px;
    }

    .service-features {
        margin-bottom: 20px;
    }

    .service-features li {
        padding: 6px 0 6px 20px;
        font-size: 0.85rem;
    }

    .service-link {
        justify-content: center;
        font-size: 0.9rem;
    }

    .services-cta {
        padding: 40px 20px;
        border-radius: 20px;
    }

    .cta-title {
        font-size: 1.5rem;
        margin-bottom: 12px;
    }

    .cta-description {
        font-size: 1rem;
        margin-bottom: 24px;
    }

    .cta-button {
        padding: 14px 28px;
        font-size: 0.95rem;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .services-section-fixed {
        padding: 50px 0;
    }

    .services-container {
        padding: 0 15px;
    }

    .services-header {
        margin-bottom: 40px;
    }

    .title-main {
        font-size: 1.8rem;
    }

    .title-sub {
        font-size: 1.1rem;
    }

    .section-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .services-grid {
        gap: 16px;
        margin-bottom: 40px;
    }

    .service-card {
        padding: 25px 16px;
    }

    .icon-wrapper {
        width: 50px;
        height: 50px;
    }

    .icon-wrapper svg {
        width: 25px;
        height: 25px;
    }

    .service-title {
        font-size: 1.1rem;
    }

    .service-description {
        font-size: 0.9rem;
    }

    .service-features li {
        font-size: 0.8rem;
    }

    .services-cta {
        padding: 30px 16px;
    }

    .cta-title {
        font-size: 1.3rem;
    }

    .cta-description {
        font-size: 0.95rem;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}

/* RTL Support */
.rtl .services-grid {
    direction: rtl;
}

.rtl .service-features li {
    padding: 8px 24px 8px 0;
}

.rtl .service-features li::before {
    left: auto;
    right: 0;
}

.rtl .service-link {
    flex-direction: row-reverse;
}

.rtl .service-link:hover svg {
    transform: translateX(-4px);
}

.rtl .cta-button {
    flex-direction: row-reverse;
}

.rtl .cta-button:hover svg {
    transform: translateX(-4px);
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .services-section-fixed *,
    .services-section-fixed *::before,
    .services-section-fixed *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .services-section-fixed {
        background: #000;
    }

    .service-card,
    .services-cta {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .service-description,
    .service-features li,
    .cta-description {
        color: #fff;
    }
}