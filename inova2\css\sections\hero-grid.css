/* Hero Grid Styles */
.hero-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 40px 20px;
    transition: all 0.3s ease-in-out;
}

.hero-grid-item {
    background: #f9f9f9;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease;
}

.hero-grid-item:hover {
    transform: translateY(-5px);
}

.hero-grid-item img {
    width: 100%;
    height: auto;
    display: block;
}

.hero-grid-item h3 {
    font-size: 1.5em;
    margin: 15px 0;
    text-align: center;
}

.hero-grid-item p {
    font-size: 1em;
    padding: 0 15px;
    text-align: center;
}

/* Smooth Scroll Transition */
html {
    scroll-behavior: smooth;
}

/* Section Transition Effects */
section {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

section.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Media Queries for Responsiveness */
@media (max-width: 768px) {
    .hero-grid {
        padding: 20px 10px;
    }

    .hero-grid-item h3 {
        font-size: 1.2em;
    }

    .hero-grid-item p {
        font-size: 0.9em;
    }
}