/* Animations for smooth transitions and dynamic effects between sections */

/* Fade In Animation */
@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }

}

/* Slide In Animation */
@keyframes slideIn {
    0% {
        opacity: 0;
        transform: translateX(-100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scale Up Animation */
@keyframes scaleUp {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Section Styles */
section {
    opacity: 0;
    animation-duration: 1s;
    animation-fill-mode: forwards;
}

/* About Section */
.about {
    animation-name: fadeIn;
}

/* Activities Section */
.activities {
    animation-name: slideIn;
}

/* Rebranding Section */
.rebranding {
    animation-name: scaleUp;
}

/* Hero Section */
.hero {
    animation-name: fadeIn;
}

/* Apply animations on scroll */
[data-aos] {
    opacity: 0;
    transition-property: opacity, transform;
    transition-duration: 0.6s;
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

[data-aos="fade-up"] {
    transform: translateY(20px);
}

[data-aos="fade-right"] {
    transform: translateX(20px);
}

[data-aos="fade-left"] {
    transform: translateX(-20px);
}

/* Smooth Scroll */
html {
    scroll-behavior: smooth;
}