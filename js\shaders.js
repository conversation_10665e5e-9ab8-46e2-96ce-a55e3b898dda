class Shaders {
    constructor() {
        this.initShaders();
    }

    initShaders() {
        // Vertex shader
        this.vertexShader = `
            varying vec2 vUv;
            varying vec3 vPosition;
            
            void main() {
                vUv = uv;
                vPosition = position;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;

        // Fragment shader for energy effect
        this.energyFragmentShader = `
            uniform float time;
            uniform vec3 color;
            varying vec2 vUv;
            varying vec3 vPosition;
            
            void main() {
                vec2 uv = vUv;
                
                // Create energy wave effect
                float wave = sin(uv.x * 10.0 + time) * 0.5 + 0.5;
                wave *= sin(uv.y * 8.0 + time * 0.5) * 0.5 + 0.5;
                
                // Color gradient
                vec3 finalColor = color * wave;
                
                // Add glow effect
                float glow = pow(wave, 3.0) * 2.0;
                finalColor += glow * vec3(0.1, 0.4, 1.0);
                
                gl_FragColor = vec4(finalColor, wave * 0.8);
            }
        `;

        this.setupMaterials();
        this.applyShaders();
    }

    setupMaterials() {
        this.energyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                color: { value: new THREE.Color(0x00ffff) }
            },
            vertexShader: this.vertexShader,
            fragmentShader: this.energyFragmentShader,
            transparent: true,
            side: THREE.DoubleSide
        });
    }

    applyShaders() {
        // Apply to hero section background
        const heroCanvas = document.querySelector('#hero-canvas');
        if (!heroCanvas) return;

        const renderer = new THREE.WebGLRenderer({ canvas: heroCanvas, alpha: true });
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

        // Create geometry for shader
        const geometry = new THREE.PlaneGeometry(2, 2);
        const mesh = new THREE.Mesh(geometry, this.energyMaterial);
        scene.add(mesh);

        // Animation loop
        const animate = () => {
            requestAnimationFrame(animate);
            this.energyMaterial.uniforms.time.value += 0.01;
            renderer.render(scene, camera);
        };

        animate();

        // Handle resize
        window.addEventListener('resize', () => {
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            camera.aspect = width / height;
            camera.updateProjectionMatrix();
            
            renderer.setSize(width, height);
        });
    }
}

// Initialize shaders
window.addEventListener('load', () => new Shaders());