/* ===================================
   INOVA ENERGY HERO SECTION - ULTIMATE
   Professional & Perfect Design
   =================================== */

/* Hero Section Base */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg,
            rgba(0, 31, 63, 1) 0%,
            rgba(0, 15, 30, 0.98) 30%,
            rgba(0, 31, 63, 0.95) 70%,
            rgba(0, 15, 30, 1) 100%);
    padding: 0;
    margin: 0;
}

/* Advanced Background System */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
}

.background-layers {
    position: relative;
    width: 100%;
    height: 100%;
}

.gradient-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 163, 255, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(0, 255, 209, 0.08) 0%, transparent 50%),
        linear-gradient(45deg, transparent 40%, rgba(0, 163, 255, 0.04) 60%, transparent 80%);
    animation: gradientFlow 15s ease-in-out infinite alternate;
}

@keyframes gradientFlow {
    0% {
        opacity: 0.6;
        transform: scale(1) rotate(0deg);
    }

    100% {
        opacity: 1;
        transform: scale(1.05) rotate(2deg);
    }
}

.particle-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

#energyParticles {
    width: 100%;
    height: 100%;
    opacity: 0.4;
}

.geometric-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.floating-shape {
    position: absolute;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.08),
            rgba(0, 255, 209, 0.05));
    border-radius: 50%;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    animation: floatAnimation 20s ease-in-out infinite;
}

.shape-1 {
    width: 120px;
    height: 120px;
    top: 15%;
    left: 8%;
    animation-delay: 0s;
}

.shape-2 {
    width: 80px;
    height: 80px;
    top: 70%;
    right: 12%;
    animation-delay: -5s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 40%;
    left: 5%;
    animation-delay: -10s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 80%;
    right: 25%;
    animation-delay: -15s;
}

@keyframes floatAnimation {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.3;
    }

    25% {
        transform: translateY(-30px) rotate(90deg);
        opacity: 0.6;
    }

    50% {
        transform: translateY(-60px) rotate(180deg);
        opacity: 0.4;
    }

    75% {
        transform: translateY(-30px) rotate(270deg);
        opacity: 0.7;
    }
}

/* Hero Container */
.hero-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    height: 100vh;
    display: flex;
    align-items: center;
}

.hero-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    width: 100%;
    min-height: 600px;
}

/* Content Section */
.hero-content-section {
    position: relative;
    z-index: 5;
}

.content-wrapper {
    max-width: 600px;
}

/* Company Introduction */
.company-intro {
    margin-bottom: 40px;
}

.intro-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 163, 255, 0.1);
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50px;
    padding: 8px 16px;
    margin-bottom: 24px;
    font-size: 0.9rem;
    color: var(--accent);
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.intro-badge:hover {
    background: rgba(0, 163, 255, 0.15);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-2px);
}

.badge-icon {
    font-size: 1.1rem;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

.badge-text {
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Hero Title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
    color: var(--text-light);
}

.title-line-1,
.title-line-3 {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
}

.title-line-2 {
    display: block;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    position: relative;
    margin: 8px 0;
}

.title-line-2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 2px;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% {
        width: 100px;
        opacity: 0.7;
    }

    100% {
        width: 120px;
        opacity: 1;
    }
}

/* Hero Description */
.hero-description {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
    font-weight: 400;
    max-width: 500px;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(0, 163, 255, 0.2);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 163, 255, 0.15);
}

.feature-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 163, 255, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    background: rgba(0, 163, 255, 0.2);
    transform: scale(1.1);
}

.feature-content h3 {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 4px;
    line-height: 1.2;
}

.feature-content p {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.3;
    margin: 0;
}

/* Hero Actions */
.hero-actions {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 50px;
}

.cta-primary {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    text-decoration: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-primary:hover::before {
    left: 100%;
}

.cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 163, 255, 0.4);
}

.btn-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
}

.cta-primary:hover .btn-icon {
    transform: translateX(4px);
}

.btn-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.cta-secondary {
    display: inline-flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 16px 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.cta-secondary:hover {
    color: var(--accent);
    border-color: var(--accent);
    background: rgba(0, 255, 209, 0.1);
    transform: translateY(-2px);
}

/* Hero Statistics */
.hero-stats {
    display: flex;
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Logo Section */
.hero-logo-section {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.logo-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Logo Effects */
.logo-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
}

.field-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    border: 1px solid rgba(0, 163, 255, 0.2);
    border-radius: 50%;
    animation: ringExpand 8s ease-in-out infinite;
}

.ring-1 {
    width: 200px;
    height: 200px;
    margin: -100px 0 0 -100px;
    animation-delay: 0s;
}

.ring-2 {
    width: 300px;
    height: 300px;
    margin: -150px 0 0 -150px;
    animation-delay: -2s;
}

.ring-3 {
    width: 400px;
    height: 400px;
    margin: -200px 0 0 -200px;
    animation-delay: -4s;
}

.ring-4 {
    width: 500px;
    height: 500px;
    margin: -250px 0 0 -250px;
    animation-delay: -6s;
}

@keyframes ringExpand {

    0%,
    100% {
        opacity: 0.2;
        transform: scale(0.8);
        border-color: rgba(0, 163, 255, 0.2);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.1);
        border-color: rgba(0, 255, 209, 0.4);
    }
}

.particle-burst {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    margin: -150px 0 0 -150px;
    background:
        radial-gradient(circle at 30% 30%, rgba(0, 163, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(0, 255, 209, 0.08) 0%, transparent 50%);
    border-radius: 50%;
    animation: particleSpin 12s linear infinite;
}

@keyframes particleSpin {
    0% {
        transform: rotate(0deg);
        opacity: 0.3;
    }

    100% {
        transform: rotate(360deg);
        opacity: 0.7;
    }
}

.glow-orb {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    background: radial-gradient(circle,
            rgba(0, 163, 255, 0.3) 0%,
            rgba(0, 255, 209, 0.2) 50%,
            transparent 70%);
    border-radius: 50%;
    animation: orbPulse 4s ease-in-out infinite;
}

@keyframes orbPulse {

    0%,
    100% {
        opacity: 0.4;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.2);
    }
}

/* Main Logo */
.main-logo-wrapper {
    position: relative;
    z-index: 5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}

.logo-image {
    position: relative;
    width: 250px;
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-logo-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 0 30px rgba(0, 163, 255, 0.4));
    transition: all 0.4s ease;
    animation: logoFloat 6s ease-in-out infinite;
}

@keyframes logoFloat {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
    }

    50% {
        transform: translateY(-15px) rotate(2deg);
    }
}

.logo-image:hover .hero-logo-img {
    filter: drop-shadow(0 0 50px rgba(0, 255, 209, 0.6));
    transform: scale(1.05) rotate(5deg);
}

/* Logo Enhancement */
.logo-enhancement {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.enhancement-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 280px;
    height: 280px;
    margin: -140px 0 0 -140px;
    background: radial-gradient(circle,
            rgba(0, 163, 255, 0.1) 0%,
            transparent 70%);
    border-radius: 50%;
    animation: enhancementPulse 3s ease-in-out infinite alternate;
}

@keyframes enhancementPulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }

    100% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

.enhancement-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 163, 255, 0.4), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0, 255, 209, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 163, 255, 0.5), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: particleMove 15s linear infinite;
    opacity: 0.6;
}

@keyframes particleMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-100px, -100px);
    }
}

/* Company Branding */
.company-branding {
    text-align: center;
    position: relative;
    z-index: 5;
}

.brand-name {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 8px;
    text-shadow: 0 0 30px rgba(0, 163, 255, 0.3);
    animation: brandGlow 4s ease-in-out infinite alternate;
}

@keyframes brandGlow {
    0% {
        text-shadow: 0 0 30px rgba(0, 163, 255, 0.3);
    }

    100% {
        text-shadow: 0 0 50px rgba(0, 255, 209, 0.5);
    }
}

.brand-tagline {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 16px;
    animation: taglineFloat 3s ease-in-out infinite alternate;
}

@keyframes taglineFloat {
    0% {
        opacity: 0.8;
        transform: translateY(0);
    }

    100% {
        opacity: 1;
        transform: translateY(-3px);
    }
}

.brand-line {
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    margin: 0 auto;
    border-radius: 1px;
    animation: lineGlow 2s ease-in-out infinite alternate;
}

@keyframes lineGlow {
    0% {
        width: 60px;
        opacity: 0.7;
    }

    100% {
        width: 80px;
        opacity: 1;
    }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.scroll-indicator:hover {
    color: var(--accent);
    transform: translateX(-50%) translateY(-5px);
}

.scroll-text {
    font-size: 0.85rem;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.scroll-arrow {
    width: 24px;
    height: 24px;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(8px);
    }
}

.scroll-arrow svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
    .hero-container {
        padding: 0 60px;
    }

    .hero-layout {
        gap: 100px;
    }

    .hero-title {
        font-size: 4rem;
    }

    .hero-description {
        font-size: 1.3rem;
    }

    .logo-image {
        width: 300px;
        height: 300px;
    }

    .brand-name {
        font-size: 3rem;
    }
}

/* Tablet Landscape (1024px - 1399px) */
@media (max-width: 1399px) and (min-width: 1024px) {
    .hero-container {
        padding: 0 40px;
    }

    .hero-layout {
        gap: 60px;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .feature-item {
        padding: 14px;
    }

    .hero-actions {
        gap: 16px;
    }

    .hero-stats {
        gap: 30px;
    }

    .logo-image {
        width: 220px;
        height: 220px;
    }

    .brand-name {
        font-size: 2.2rem;
    }
}

/* Tablet Portrait (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .hero-section {
        min-height: 100vh;
        padding: 80px 0;
    }

    .hero-container {
        padding: 0 30px;
        height: auto;
    }

    .hero-layout {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
        min-height: auto;
    }

    .hero-logo-section {
        order: 1;
    }

    .hero-content-section {
        order: 2;
    }

    .content-wrapper {
        max-width: 100%;
    }

    .hero-title {
        font-size: 2.8rem;
        line-height: 1.2;
    }

    .hero-description {
        font-size: 1.1rem;
        max-width: 600px;
        margin: 0 auto 40px;
    }

    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        max-width: 500px;
        margin: 0 auto 40px;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        padding: 12px 8px;
        gap: 8px;
    }

    .feature-icon {
        width: 36px;
        height: 36px;
    }

    .feature-content h3 {
        font-size: 0.85rem;
    }

    .feature-content p {
        font-size: 0.75rem;
    }

    .hero-actions {
        justify-content: center;
        gap: 16px;
        margin-bottom: 40px;
    }

    .hero-stats {
        justify-content: center;
        gap: 40px;
    }

    .logo-image {
        width: 200px;
        height: 200px;
    }

    .brand-name {
        font-size: 2rem;
    }

    .brand-tagline {
        font-size: 0.9rem;
    }

    .scroll-indicator {
        bottom: 30px;
    }
}

/* Mobile Large (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
    .hero-section {
        min-height: 100vh;
        padding: 60px 0 40px;
    }

    .hero-container {
        padding: 0 20px;
    }

    .hero-layout {
        gap: 40px;
    }

    .company-intro {
        margin-bottom: 30px;
    }

    .intro-badge {
        padding: 6px 12px;
        font-size: 0.8rem;
        margin-bottom: 20px;
    }

    .hero-title {
        font-size: 2.2rem;
        line-height: 1.3;
        margin-bottom: 20px;
    }

    .title-line-2::after {
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
    }

    .hero-description {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 30px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        margin-bottom: 30px;
    }

    .feature-item {
        flex-direction: row;
        text-align: left;
        padding: 12px 16px;
        gap: 12px;
    }

    .feature-icon {
        width: 32px;
        height: 32px;
    }

    .feature-content h3 {
        font-size: 0.9rem;
        margin-bottom: 2px;
    }

    .feature-content p {
        font-size: 0.8rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 30px;
    }

    .cta-primary,
    .cta-secondary {
        width: 100%;
        max-width: 280px;
        justify-content: center;
        padding: 14px 24px;
    }

    .hero-stats {
        gap: 30px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .logo-image {
        width: 180px;
        height: 180px;
    }

    .brand-name {
        font-size: 1.8rem;
    }

    .brand-tagline {
        font-size: 0.85rem;
        letter-spacing: 1px;
    }

    .scroll-indicator {
        bottom: 20px;
    }

    .scroll-text {
        font-size: 0.8rem;
    }

    .scroll-arrow {
        width: 20px;
        height: 20px;
    }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
    .hero-section {
        padding: 50px 0 30px;
    }

    .hero-container {
        padding: 0 15px;
    }

    .hero-layout {
        gap: 30px;
    }

    .intro-badge {
        padding: 5px 10px;
        font-size: 0.75rem;
        margin-bottom: 16px;
    }

    .hero-title {
        font-size: 1.9rem;
        line-height: 1.3;
        margin-bottom: 16px;
    }

    .title-line-2::after {
        width: 60px;
    }

    .hero-description {
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 25px;
    }

    .features-grid {
        gap: 10px;
        margin-bottom: 25px;
    }

    .feature-item {
        padding: 10px 12px;
        gap: 10px;
    }

    .feature-icon {
        width: 28px;
        height: 28px;
    }

    .feature-content h3 {
        font-size: 0.85rem;
    }

    .feature-content p {
        font-size: 0.75rem;
    }

    .hero-actions {
        gap: 10px;
        margin-bottom: 25px;
    }

    .cta-primary,
    .cta-secondary {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .hero-stats {
        gap: 20px;
    }

    .stat-number {
        font-size: 1.6rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .logo-image {
        width: 150px;
        height: 150px;
    }

    .brand-name {
        font-size: 1.6rem;
    }

    .brand-tagline {
        font-size: 0.8rem;
        letter-spacing: 0.5px;
    }

    .brand-line {
        width: 50px;
    }

    /* Reduce animations for better performance */
    .floating-shape,
    .field-ring,
    .particle-burst,
    .glow-orb {
        animation-duration: 8s;
    }

    .hero-logo-img {
        animation-duration: 4s;
    }
}

/* RTL Support */
.rtl .hero-layout {
    direction: rtl;
}

.rtl .feature-item {
    flex-direction: row-reverse;
}

.rtl .hero-actions {
    flex-direction: row-reverse;
}

.rtl .title-line-2::after {
    left: auto;
    right: 0;
}

@media (max-width: 767px) {
    .rtl .hero-actions {
        flex-direction: column;
    }

    .rtl .title-line-2::after {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
    }

    .rtl .feature-item {
        flex-direction: row;
        text-align: right;
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {

    .hero-section *,
    .hero-section *::before,
    .hero-section *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .hero-section {
        background: #000;
    }

    .intro-badge,
    .feature-item,
    .cta-secondary {
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.1);
    }

    .hero-description,
    .feature-content p,
    .stat-label {
        color: #fff;
    }
}