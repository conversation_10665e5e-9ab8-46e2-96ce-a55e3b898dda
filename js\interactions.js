class Interactions {
    constructor() {
        this.initNavigation();
        this.initHeroInteractions();
        this.initAboutInteractions();
        this.initRebrandingInteractions();
        this.initActivitiesInteractions();
    }

    initNavigation() {
        const nav = document.querySelector('nav');
        let lastScroll = 0;

        // Handle navigation visibility on scroll
        window.addEventListener('scroll', () => {
            const currentScroll = window.pageYOffset;
            
            if (currentScroll > lastScroll && currentScroll > 100) {
                nav.classList.add('nav-hidden');
            } else {
                nav.classList.remove('nav-hidden');
            }
            
            lastScroll = currentScroll;
        });

        // Smooth scroll to sections
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', e => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                
                window.scrollTo({
                    top: target.offsetTop - 100,
                    behavior: 'smooth'
                });
            });
        });
    }

    initHeroInteractions() {
        const hero = document.querySelector('#hero');
        
        // Parallax effect
        window.addEventListener('mousemove', e => {
            const mouseX = e.clientX / window.innerWidth - 0.5;
            const mouseY = e.clientY / window.innerHeight - 0.5;
            
            gsap.to(hero, {
                duration: 1,
                x: mouseX * 50,
                y: mouseY * 50,
                ease: 'power1.out'
            });
        });
    }

    initAboutInteractions() {
        const aboutVisual = document.querySelector('#about-visualization');
        
        // Interactive visualization
        aboutVisual.addEventListener('mousemove', e => {
            const rect = aboutVisual.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // Update visualization based on mouse position
            this.updateVisualization(x, y);
        });
    }

    updateVisualization(x, y) {
        // Update about section visualization
        const canvas = document.querySelector('#about-visualization canvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw interactive elements
        ctx.beginPath();
        ctx.arc(x, y, 20, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(0, 255, 255, 0.2)';
        ctx.fill();
    }

    initRebrandingInteractions() {
        const timeline = document.querySelector('.timeline-container');
        const fabricCanvas = new fabric.Canvas('rebranding-canvas');
        
        // Add interactive timeline elements
        const timelinePoints = [2023, 2024, 2025].map(year => {
            return new fabric.Circle({
                left: (year - 2023) * 100,
                top: 50,
                radius: 10,
                fill: '#00ff00',
                selectable: false
            });
        });

        timelinePoints.forEach(point => fabricCanvas.add(point));
    }

    initActivitiesInteractions() {
        document.querySelectorAll('.activity-item').forEach(item => {
            item.addEventListener('mouseenter', () => {
                // Scale up effect
                gsap.to(item, {
                    scale: 1.05,
                    duration: 0.3,
                    ease: 'power2.out'
                });

                // Particle effect
                this.createParticleEffect(item);
            });

            item.addEventListener('mouseleave', () => {
                gsap.to(item, {
                    scale: 1,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });
        });
    }

    createParticleEffect(element) {
        const rect = element.getBoundingClientRect();
        const particles = document.createElement('div');
        particles.className = 'particles';
        
        for (let i = 0; i < 10; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particles.appendChild(particle);
            
            gsap.to(particle, {
                x: (Math.random() - 0.5) * 100,
                y: (Math.random() - 0.5) * 100,
                opacity: 0,
                duration: 1,
                ease: 'power2.out'
            });
        }
        
        element.appendChild(particles);
        setTimeout(() => particles.remove(), 1000);
    }
}

// Initialize interactions
window.addEventListener('load', () => new Interactions());