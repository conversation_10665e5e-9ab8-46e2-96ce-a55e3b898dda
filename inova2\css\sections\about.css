/* About Section Styles */

.about-section {
    padding: 60px 20px;
    background-color: #f9f9f9;
    position: relative;
    overflow: hidden;
}

.about-section h2 {
    font-size: 2.5rem;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
    transition: transform 0.5s ease-in-out;
}

.about-section p {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.6;
    text-align: center;
    margin: 0 auto;
    max-width: 800px;
    transition: opacity 0.5s ease-in-out;
}

.about-section .about-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.about-section .about-image {
    width: 100%;
    max-width: 400px;
    margin: 20px 0;
    border-radius: 10px;
    transition: transform 0.5s ease;
}

.about-section .about-image:hover {
    transform: scale(1.05);
}

.about-section.visible h2,
.about-section.visible p {
    transform: translateY(0);
    opacity: 1;
}

.about-section.hidden h2,
.about-section.hidden p {
    transform: translateY(20px);
    opacity: 0;
}

/* Smooth Scroll Transition */
html {
    scroll-behavior: smooth;
}

body {
    margin: 0;
    font-family: 'Vazirmatn', sans-serif;
}

@media (max-width: 768px) {
    .about-section h2 {
        font-size: 2rem;
    }

    .about-section p {
        font-size: 1rem;
    }
}