/* Container Styles */
.content-grid.compact {
    max-width: 100%;
    padding: 1rem;
}

.circular-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding: 0.5rem;
    justify-items: center;
    align-items: center;
}

/* Feature Circle Styles */
.feature-circle {
    width: 140px;
    height: 140px;
    position: relative;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
    transition: all 0.4s ease;
}

/* Common Text Styles */
.feature-title {
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0.5rem 0 0.25rem;
}

.feature-desc {
    color: var(--text-muted);
    font-size: 0.7rem;
    line-height: 1.2;
}

/* Innovation Circle Effects */
.feature-circle.innovation {
    border: 2px solid transparent;
    background: linear-gradient(45deg, rgba(64, 48, 232, 0.1), rgba(48, 232, 214, 0.1));
}

.innovation .pulse-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    animation: pulse 2s infinite;
    border: 2px solid rgba(64, 48, 232, 0.3);
}

.innovation .particle-effect {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(64, 48, 232, 0.2) 0%, transparent 70%);
    animation: rotate 10s linear infinite;
}

/* Global Presence Circle Effects */
.feature-circle.global {
    border: 2px solid rgba(48, 232, 214, 0.3);
}

.global .rotating-border {
    position: absolute;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent 0%,
        rgba(48, 232, 214, 0.3) 50%,
        transparent 100%
    );
    animation: rotate 8s linear infinite;
}

.global .glow-effect {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(48, 232, 214, 0.2) 0%, transparent 70%);
    animation: glow 3s ease-in-out infinite;
}

/* Sustainable Energy Circle Effects */
.feature-circle.sustainable {
    border: 2px solid rgba(64, 48, 232, 0.3);
    background: linear-gradient(135deg, rgba(48, 232, 214, 0.1), rgba(64, 48, 232, 0.1));
}

.sustainable .energy-waves {
    position: absolute;
    width: 150%;
    height: 150%;
    background: repeating-radial-gradient(
        circle at center,
        transparent 0%,
        rgba(48, 232, 214, 0.1) 10%,
        transparent 20%
    );
    animation: waves 4s linear infinite;
}

.sustainable .floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(64, 48, 232, 0.2) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(1.3); opacity: 0; }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes glow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

@keyframes waves {
    0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Hover Effects */
.feature-circle:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .circular-features {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .circular-features {
        grid-template-columns: 1fr;
    }
    
    .feature-circle {
        width: 120px;
        height: 120px;
    }
}


