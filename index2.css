/* Energy Sections Styles */
.energy-section {
    height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, rgba(0,0,0,0.9), rgba(0,31,63,0.9));
}

.particle-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.section-content {
    position: relative;
    z-index: 2;
    padding: 3rem;
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    text-align: center;
}

.glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.energy-icon {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.energy-icon svg {
    width: 48px;
    height: 48px;
    fill: white;
    z-index: 2;
}

/* Pulse animations for each energy type */
.solar-pulse,
.wind-pulse,
.hydrogen-pulse,
.storage-pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.solar-pulse { background: rgba(255, 190, 0, 0.3); }
.wind-pulse { background: rgba(0, 191, 255, 0.3); }
.hydrogen-pulse { background: rgba(0, 255, 127, 0.3); }
.storage-pulse { background: rgba(147, 112, 219, 0.3); }

.section-title {
    font-size: 2.5rem;
    color: white;
    margin-bottom: 1rem;
    font-weight: 700;
}

.section-description {
    font-size: 1.2rem;
    color: rgba(255