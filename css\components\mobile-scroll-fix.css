/* ===================================
   MOBILE SCROLL FIX
   Fix horizontal scrolling issues on mobile
   =================================== */

/* Global overflow fix */
html,
body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    position: relative;
}

/* Container fixes */
.container,
.hero-container,
.about-container,
.services-container,
.projects-container,
.activities-container,
.rebranding-container,
.contact-container,
.footer-container {
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box;
}

/* Section fixes */
section {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    box-sizing: border-box;
}

/* Grid fixes */
.hero-grid,
.about-grid,
.services-grid,
.projects-grid,
.activities-grid {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Card fixes */
.service-card,
.project-card,
.activity-card,
.story-card,
.values-card {
    max-width: 100% !important;
    box-sizing: border-box;
}

/* Animation fixes */
@media (max-width: 768px) {

    /* Prevent animations from causing overflow */
    .energy-ring,
    .floating-shape,
    .particle-field,
    .background-layers {
        max-width: 100vw !important;
        overflow: hidden !important;
    }

    /* Fix transform animations */
    .hero-logo-img,
    .feature-circle,
    .stat-item {
        transform-origin: center center !important;
    }

    /* Reduce animation scale to prevent overflow */
    .hero-logo-img:hover,
    .service-card:hover,
    .project-card:hover {
        transform: scale(1.02) !important;
    }
}

/* Text overflow fixes */
.hero-title,
.section-title,
.project-title,
.service-title {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Image fixes */
img {
    max-width: 100% !important;
    height: auto !important;
    box-sizing: border-box;
}

/* Background fixes */
.hero-background,
.about-background,
.services-background {
    width: 100vw !important;
    max-width: 100vw !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    overflow: hidden !important;
}

/* Particle system fixes */
.particle-field,
.floating-particles {
    width: 100% !important;
    max-width: 100vw !important;
    overflow: hidden !important;
}

/* Mobile specific fixes */
@media (max-width: 480px) {

    /* Extra small mobile fixes */
    * {
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    /* Padding adjustments */
    .container,
    .hero-container,
    .about-container,
    .services-container,
    .projects-container,
    .activities-container,
    .rebranding-container,
    .contact-container {
        padding-left: 15px !important;
        padding-right: 15px !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    /* Font size adjustments to prevent overflow */
    .hero-title {
        font-size: 1.8rem !important;
        line-height: 1.2 !important;
        margin-bottom: 15px !important;
    }

    .section-title {
        font-size: 1.6rem !important;
        line-height: 1.3 !important;
        margin-bottom: 20px !important;
    }

    .title-main {
        font-size: 1.5rem !important;
        line-height: 1.2 !important;
    }

    .title-sub {
        font-size: 1.2rem !important;
        line-height: 1.3 !important;
    }

    /* Section spacing */
    section {
        padding: 40px 0 !important;
        margin: 0 !important;
    }

    /* Card spacing */
    .service-card,
    .project-card,
    .activity-card,
    .story-card,
    .values-card {
        margin-bottom: 20px !important;
        padding: 20px 15px !important;
    }

    /* Button adjustments */
    .cta-primary,
    .cta-secondary,
    .lang-toggle,
    .mobile-lang-toggle {
        padding: 12px 20px !important;
        font-size: 0.9rem !important;
        min-height: 44px !important;
    }
}

/* RTL specific fixes */
.rtl {
    direction: rtl;
    text-align: right;
}

.rtl .hero-grid,
.rtl .about-grid,
.rtl .services-grid {
    direction: rtl;
}

/* Performance optimizations */
@media (max-width: 768px) {

    /* Reduce complex animations */
    .energy-ring {
        animation-duration: 30s !important;
    }

    .gradient-layer {
        animation-duration: 40s !important;
    }

    /* Hide complex elements on small screens */
    .particle-3,
    .particle-4,
    .particle-7,
    .particle-8 {
        display: none !important;
    }

    /* Simplify backgrounds */
    .hero-background::before,
    .about-background::before {
        opacity: 0.3 !important;
    }
}

/* Scrollbar fixes */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 163, 255, 0.5);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 163, 255, 0.7);
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {

    /* Remove hover effects on touch devices */
    .service-card:hover,
    .project-card:hover,
    .activity-card:hover {
        transform: none !important;
        box-shadow: none !important;
    }

    /* Optimize touch scrolling */
    body {
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
    }
}

/* Landscape mobile fixes */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        min-height: 100vh;
        padding: 20px 0;
    }

    .hero-title {
        font-size: 2.5rem !important;
    }
}

/* High DPI fixes */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {

    /* Optimize for high DPI screens */
    .hero-logo-img,
    .site-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}