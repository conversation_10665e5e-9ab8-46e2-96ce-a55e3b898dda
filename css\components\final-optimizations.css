/* ===================================
   FINAL OPTIMIZATIONS
   Complete website optimization and fixes
   =================================== */

/* Global RTL/LTR Support */
html[dir="rtl"] {
    direction: rtl;
}

html[dir="ltr"] {
    direction: ltr;
}

body.rtl {
    direction: rtl;
    text-align: right;
}

body.ltr {
    direction: ltr;
    text-align: left;
}

/* Typography Direction */
.rtl h1,
.rtl h2,
.rtl h3,
.rtl h4,
.rtl h5,
.rtl h6,
.rtl p,
.rtl div,
.rtl span {
    text-align: right;
}

.ltr h1,
.ltr h2,
.ltr h3,
.ltr h4,
.ltr h5,
.ltr h6,
.ltr p,
.ltr div,
.ltr span {
    text-align: left;
}

/* Navigation Direction */
.rtl .nav-menu {
    flex-direction: row-reverse;
}

.ltr .nav-menu {
    flex-direction: row;
}

.rtl .mobile-nav-menu {
    text-align: right;
}

.ltr .mobile-nav-menu {
    text-align: left;
}

/* Header Direction */
.rtl .header-container {
    flex-direction: row-reverse;
}

.ltr .header-container {
    flex-direction: row;
}

.rtl .site-logo {
    margin-right: 0;
    margin-left: auto;
}

.ltr .site-logo {
    margin-left: 0;
    margin-right: auto;
}

/* Hero Section Direction */
.rtl .hero-title-professional,
.rtl .hero-description-professional {
    text-align: center;
}

.ltr .hero-title-professional,
.ltr .hero-description-professional {
    text-align: center;
}

.rtl .cta-buttons-container {
    flex-direction: row-reverse;
}

.ltr .cta-buttons-container {
    flex-direction: row;
}

/* Grid Direction */
.rtl .about-content-grid,
.rtl .rebranding-content-grid,
.rtl .contact-content,
.rtl .activities-grid,
.rtl .services-grid-professional {
    direction: rtl;
}

.ltr .about-content-grid,
.ltr .rebranding-content-grid,
.ltr .contact-content,
.ltr .activities-grid,
.ltr .services-grid-professional {
    direction: ltr;
}

/* Card Direction */
.rtl .story-header,
.rtl .values-header,
.rtl .card-header,
.rtl .card-header-professional {
    flex-direction: row-reverse;
}

.ltr .story-header,
.ltr .values-header,
.ltr .card-header,
.ltr .card-header-professional {
    flex-direction: row;
}

.rtl .timeline-item,
.rtl .value-item,
.rtl .benefit-item,
.rtl .feature-item,
.rtl .feature-item-professional,
.rtl .info-item {
    flex-direction: row-reverse;
    text-align: right;
}

.ltr .timeline-item,
.ltr .value-item,
.ltr .benefit-item,
.ltr .feature-item,
.ltr .feature-item-professional,
.ltr .info-item {
    flex-direction: row;
    text-align: left;
}

/* Form Direction */
.rtl .form-group {
    text-align: right;
}

.ltr .form-group {
    text-align: left;
}

.rtl .form-group label {
    text-align: right;
}

.ltr .form-group label {
    text-align: left;
}

.rtl .form-group input,
.rtl .form-group textarea {
    text-align: right;
}

.ltr .form-group input,
.ltr .form-group textarea {
    text-align: left;
}

/* Button Direction */
.rtl .cta-primary,
.rtl .cta-secondary,
.rtl .cta-primary-advanced,
.rtl .cta-secondary-advanced,
.rtl .cta-button-professional,
.rtl .service-cta-professional {
    flex-direction: row-reverse;
}

.ltr .cta-primary,
.ltr .cta-secondary,
.ltr .cta-primary-advanced,
.ltr .cta-secondary-advanced,
.ltr .cta-button-professional,
.ltr .service-cta-professional {
    flex-direction: row;
}

.rtl .button-icon {
    margin-left: 0;
    margin-right: 10px;
}

.ltr .button-icon {
    margin-right: 0;
    margin-left: 10px;
}

/* Language Toggle Direction */
.rtl .lang-toggle {
    flex-direction: row-reverse;
}

.ltr .lang-toggle {
    flex-direction: row;
}

/* Footer Direction */
.rtl .footer-content {
    direction: rtl;
    text-align: right;
}

.ltr .footer-content {
    direction: ltr;
    text-align: left;
}

.rtl .footer-links {
    flex-direction: row-reverse;
}

.ltr .footer-links {
    flex-direction: row;
}

/* Number Animation Fix */
.stat-number,
.stat-counter {
    font-variant-numeric: tabular-nums;
}

/* Prevent NaN in counters */
.stat-number[data-value=""],
.stat-counter[data-target=""] {
    display: none;
}

/* Smooth Transitions */
* {
    transition: direction 0.3s ease;
}

/* Performance Optimizations */
.hero-section-professional,
.about-section-ultimate,
.services-section-professional,
.activities-section-fixed,
.projects-section-professional,
.rebranding-section-ultimate,
.contact-section-fixed {
    contain: layout style paint;
    will-change: transform;
    width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

/* Hardware Acceleration */
.activity-card,
.service-card-professional,
.story-card,
.values-card,
.info-card,
.form-card,
.benefit-item {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Scroll Performance */
html {
    scroll-behavior: smooth;
}

/* High DPI Support */
@media (-webkit-min-device-pixel-ratio: 2) {

    .hero-logo-main,
    .site-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}

/* Focus Accessibility */
.cta-primary:focus,
.cta-secondary:focus,
.cta-primary-advanced:focus,
.cta-secondary-advanced:focus,
.cta-button-professional:focus,
.service-cta-professional:focus,
.submit-btn:focus,
.lang-toggle:focus {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
}

/* Mobile Optimizations */
@media (max-width: 767px) {

    .rtl .story-header,
    .rtl .values-header,
    .rtl .card-header,
    .rtl .card-header-professional {
        flex-direction: column;
        text-align: center;
    }

    .ltr .story-header,
    .ltr .values-header,
    .ltr .card-header,
    .ltr .card-header-professional {
        flex-direction: column;
        text-align: center;
    }

    .rtl .timeline-item,
    .rtl .value-item,
    .rtl .benefit-item,
    .rtl .info-item {
        flex-direction: column;
        text-align: center;
    }

    .ltr .timeline-item,
    .ltr .value-item,
    .ltr .benefit-item,
    .ltr .info-item {
        flex-direction: column;
        text-align: center;
    }

    .rtl .feature-item,
    .rtl .feature-item-professional {
        flex-direction: row-reverse;
        text-align: right;
    }

    .ltr .feature-item,
    .ltr .feature-item-professional {
        flex-direction: row;
        text-align: left;
    }

    .rtl .cta-buttons-container {
        flex-direction: column;
        align-items: center;
    }

    .ltr .cta-buttons-container {
        flex-direction: column;
        align-items: center;
    }
}

/* Small Mobile */
@media (max-width: 480px) {

    .rtl .feature-item,
    .rtl .feature-item-professional {
        flex-direction: row-reverse;
        text-align: right;
    }

    .ltr .feature-item,
    .ltr .feature-item-professional {
        flex-direction: row;
        text-align: left;
    }

    .rtl .header-container {
        flex-direction: row-reverse;
    }

    .ltr .header-container {
        flex-direction: row;
    }
}

/* Animation Performance */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .rtl * {
        direction: rtl !important;
        text-align: right !important;
    }

    .ltr * {
        direction: ltr !important;
        text-align: left !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-light: #ffffff;
        --primary: #00a3ff;
        --accent: #00ffd1;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {

    .rtl *,
    .ltr * {
        border-color: currentColor !important;
    }
}