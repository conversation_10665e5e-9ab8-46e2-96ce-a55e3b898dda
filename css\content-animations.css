.content-body {
    position: relative;
    padding: 2rem;
    overflow: hidden;
    border-radius: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.energy-lines {
    position: absolute;
    width: 100%;
    height: 100%;
}

.energy-line {
    position: absolute;
    background: linear-gradient(90deg, rgba(0, 163, 255, 0), rgba(0, 163, 255, 0.3), rgba(0, 163, 255, 0));
    height: 1px;
    width: 100%;
    transform-origin: left;
}

.content-grid {
    position: relative;
    z-index: 1;
    display: grid;
    gap: 2rem;
    padding: 1rem;
}

.content-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 1rem;
    padding: 2rem;
    transform-origin: center;
    transition: transform 0.3s ease;
}

.content-section:hover {
    transform: translateY(-5px);
}

.section-icon {
    position: relative;
    width: 3rem;
    height: 3rem;
    margin-bottom: 1.5rem;
}

.icon-svg {
    width: 100%;
    height: 100%;
    fill: #00a3ff;
    filter: drop-shadow(0 0 10px rgba(0, 163, 255, 0.5));
}

.icon-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 163, 255, 0.2) 0%, transparent 70%);
}

.section-title {
    font-size: 1.5rem;
    color: #fff;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, #00a3ff, #00ffd1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.reasons-grid {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 1rem;
    padding: 2rem;
}

.reasons-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.reason-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.8rem;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transform-origin: center;
    transition: all 0.3s ease;
}

.reason-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: scale(1.02);
}

.reason-icon {
    width: 2.5rem;
    height: 2.5rem;
    flex-shrink: 0;
}

.split-text {
    opacity: 0;
    transform: translateY(20px);
}