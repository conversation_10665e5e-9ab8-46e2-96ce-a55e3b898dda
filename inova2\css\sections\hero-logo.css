/* Hero Logo Section Styles */

.hero-logo {
    position: relative;
    text-align: center;
    padding: 100px 20px;
    background-color: #f9f9f9; /* Light background for contrast */
    transition: background-color 0.5s ease; /* Smooth background transition */
}

.hero-logo:hover {
    background-color: #e0e0e0; /* Darker background on hover */
}

.hero-logo h1 {
    font-size: 3rem;
    color: #333; /* Dark text color */
    margin: 0;
    transition: transform 0.5s ease; /* Smooth transform transition */
}

.hero-logo h1:hover {
    transform: scale(1.05); /* Slightly enlarge on hover */
}

.hero-logo p {
    font-size: 1.5rem;
    color: #666; /* Lighter text color */
    margin-top: 10px;
    transition: color 0.5s ease; /* Smooth color transition */
}

.hero-logo p:hover {
    color: #333; /* Darker text on hover */
}

/* Dynamic Effects for Smooth Transitions */
.section {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease; /* Smooth transition for opacity and position */
}

.section.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Media Queries for Responsiveness */
@media (max-width: 768px) {
    .hero-logo h1 {
        font-size: 2.5rem; /* Smaller font size on mobile */
    }

    .hero-logo p {
        font-size: 1.2rem; /* Smaller paragraph size on mobile */
    }
}