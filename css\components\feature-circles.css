.circular-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 1rem;
    width: 100%;
    justify-items: center;
    position: relative;
    z-index: 1;
}

.feature-circle {
    position: relative;
    width: 150px; /* کوچکتر کردن سایز */
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(20, 20, 35, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    cursor: pointer;
    overflow: hidden;
}

/* افکت hover جدید */
.feature-circle:hover {
    transform: translateY(-8px) scale(1.05);
    background: rgba(25, 25, 45, 0.8);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 15px 30px rgba(0, 0, 0, 0.2),
        0 0 40px rgba(64, 48, 232, 0.3),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.feature-circle:hover .feature-icon-wrapper {
    transform: scale(1.15);
}

.feature-circle:hover .orbital-ring {
    border-color: rgba(255, 255, 255, 0.3);
    animation: orbitFast 15s linear infinite;
}

.feature-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.2rem;
    text-align: center;
}

.feature-icon-wrapper {
    position: relative;
    width: 50px;
    height: 50px;
    margin-bottom: 0.8rem;
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.feature-icon-wrapper::before {
    content: '';
    position: absolute;
    inset: -5px;
    background: radial-gradient(circle at center,
        rgba(255, 255, 255, 0.2) 0%,
        transparent 70%
    );
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-circle:hover .feature-icon-wrapper::before {
    opacity: 1;
    animation: pulseGlow 2s ease-in-out infinite;
}

.feature-title {
    font-size: 1rem;
    margin-bottom: 0.4rem;
    transition: all 0.3s ease;
}

.feature-desc {
    font-size: 0.85rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.feature-circle:hover .feature-title {
    color: #fff;
    transform: scale(1.05);
}

.feature-circle:hover .feature-desc {
    opacity: 0.9;
}

/* افکت‌های تزئینی بهبود یافته */
.feature-decorations {
    position: absolute;
    inset: 0;
    z-index: 1;
}

.orbital-ring {
    position: absolute;
    inset: -10%;
    border: 1px dashed rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.5s ease;
}

.particle-field {
    position: absolute;
    inset: -5%;
    background: radial-gradient(circle at center,
        rgba(64, 48, 232, 0.1) 0%,
        transparent 70%
    );
    opacity: 0;
    transition: opacity 0.5s ease;
}

.feature-circle:hover .particle-field {
    opacity: 1;
    animation: particleFieldRotate 10s linear infinite;
}

.glow-effect {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center,
        rgba(48, 232, 214, 0.15) 0%,
        transparent 70%
    );
    opacity: 0;
    transition: opacity 0.5s ease;
}

.feature-circle:hover .glow-effect {
    opacity: 1;
    animation: glowPulseHover 3s ease-in-out infinite;
}

/* انیمیشن‌های جدید */
@keyframes pulseGlow {
    0%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes particleFieldRotate {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.2);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

@keyframes glowPulseHover {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.2);
    }
}

@keyframes orbitFast {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .circular-features {
        gap: 1.5rem;
    }
    
    .feature-circle {
        width: 130px;
        height: 130px;
    }
}

@media (max-width: 768px) {
    .circular-features {
        gap: 1rem;
    }
    
    .feature-circle {
        width: 120px;
        height: 120px;
    }
    
    .feature-title {
        font-size: 0.9rem;
    }
    
    .feature-desc {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .circular-features {
        grid-template-columns: repeat(1, 1fr);
        gap: 1.5rem;
    }
    
    .feature-circle {
        width: 140px;
        height: 140px;
    }
}



