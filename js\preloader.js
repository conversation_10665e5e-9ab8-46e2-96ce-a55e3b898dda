class Preloader {
    constructor() {
        this.preloader = document.querySelector('.preloader');
        if (this.preloader) {
            this.init();
        }
    }

    init() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (this.preloader) {
                    this.preloader.style.opacity = '0';
                    setTimeout(() => {
                        if (this.preloader) {
                            this.preloader.style.display = 'none';
                        }
                    }, 300);
                }
            }, 500);
        });
    }
}

// Only initialize if DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new Preloader();
    });
} else {
    new Preloader();
}