// File: c:\wamp\www\inova2\js\hero-animations.js

document.addEventListener('DOMContentLoaded', function() {
    const heroSection = document.querySelector('.hero');
    const rebrandingSection = document.querySelector('.rebranding');
    const activitiesSection = document.querySelector('.activities');
    const aboutSection = document.querySelector('.about');

    // Function to add animation classes
    function addAnimation(section) {
        section.classList.add('animate-in');
    }

    // Function to remove animation classes
    function removeAnimation(section) {
        section.classList.remove('animate-in');
    }

    // Intersection Observer for smooth transitions
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                addAnimation(entry.target);
            } else {
                removeAnimation(entry.target);
            }
        });
    }, {
        threshold: 0.1 // Trigger when 10% of the section is visible
    });

    // Observe each section
    observer.observe(heroSection);
    observer.observe(rebrandingSection);
    observer.observe(activitiesSection);
    observer.observe(aboutSection);
});