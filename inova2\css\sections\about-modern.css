/* ===================================
   INOVA ENERGY ABOUT SECTION
   Professional & Engaging Design
   =================================== */

/* About Section Container */
.about-section-modern {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg,
            rgba(0, 31, 63, 0.95) 0%,
            rgba(0, 15, 30, 0.98) 50%,
            rgba(0, 31, 63, 1) 100%);
    overflow: hidden;
}

/* Background Effects */
.about-hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 163, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(0, 255, 209, 0.06) 0%, transparent 50%),
        linear-gradient(45deg, transparent 40%, rgba(0, 163, 255, 0.03) 60%, transparent 80%);
    animation: aboutBgFloat 10s ease-in-out infinite alternate;
    z-index: 1;
}

@keyframes aboutBgFloat {
    0% {
        opacity: 0.6;
        transform: scale(1) rotate(0deg);
    }

    100% {
        opacity: 1;
        transform: scale(1.02) rotate(1deg);
    }
}

/* Container */
.container.about-flex {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

/* About Introduction */
.about-intro {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
}

.about-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 24px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    display: inline-block;
}

.about-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 2px;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% {
        width: 80px;
        opacity: 0.7;
    }

    100% {
        width: 100px;
        opacity: 1;
    }
}

.about-lead-modern {
    font-size: 1.3rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.about-highlight {
    color: var(--accent);
    font-weight: 600;
    position: relative;
}

.about-desc-modern {
    font-size: 1.1rem;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.8);
    max-width: 700px;
    margin: 0 auto;
}

/* Cards Grid */
.about-cards-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-bottom: 80px;
}

/* Values Section */
.about-values-modern {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    padding: 50px 40px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.about-values-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 163, 255, 0.08),
            transparent);
    transition: left 0.8s ease;
}

.about-values-modern:hover::before {
    left: 100%;
}

.about-values-modern:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 163, 255, 0.15);
    border-color: rgba(0, 163, 255, 0.2);
}

.values-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 32px;
    position: relative;
    padding-bottom: 16px;
}

.values-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 1px;
}

/* Values List */
.values-list {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.value-modern {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.value-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary), var(--accent));
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.value-modern:hover::before {
    transform: scaleY(1);
}

.value-modern:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 163, 255, 0.2);
    transform: translateX(8px);
}

.value-modern lord-icon {
    flex-shrink: 0;
    margin-top: 4px;
}

.value-modern div h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 8px;
    line-height: 1.3;
}

.value-modern div p {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.75);
    line-height: 1.6;
}

/* Mission Section */
.about-mission-modern {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    padding: 50px 40px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.about-mission-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 255, 209, 0.08),
            transparent);
    transition: left 0.8s ease;
}

.about-mission-modern:hover::before {
    left: 100%;
}

.about-mission-modern:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 255, 209, 0.15);
    border-color: rgba(0, 255, 209, 0.2);
}

.mission-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 32px;
    position: relative;
    padding-bottom: 16px;
}

.mission-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--accent), var(--primary));
    border-radius: 1px;
}

.mission-content {
    text-align: center;
}

.mission-icon {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
}

.mission-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.85);
    font-style: italic;
    position: relative;
}

.mission-text::before,
.mission-text::after {
    content: '"';
    font-size: 2rem;
    color: var(--accent);
    font-weight: 700;
    position: absolute;
    top: -10px;
}

.mission-text::before {
    left: -20px;
}

.mission-text::after {
    right: -20px;
}

/* Statistics Section */
.about-stats-modern {
    margin-top: 60px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(0, 163, 255, 0.05),
            rgba(0, 255, 209, 0.03));
    opacity: 0;
    transition: opacity 0.4s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.15);
    border-color: rgba(0, 163, 255, 0.2);
}

.stat-icon {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
}

.stat-content {
    position: relative;
    z-index: 2;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 8px;
    display: block;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Vision Section */
.about-vision-modern {
    margin-top: 80px;
}

.vision-container {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    padding: 60px 50px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.vision-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 30%, rgba(0, 163, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(0, 255, 209, 0.06) 0%, transparent 50%);
    animation: visionGlow 8s ease-in-out infinite alternate;
}

@keyframes visionGlow {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }

    100% {
        opacity: 1;
        transform: scale(1.02);
    }
}

.vision-header {
    margin-bottom: 40px;
    position: relative;
    z-index: 2;
}

.vision-icon {
    margin-bottom: 24px;
}

.vision-title {
    font-size: 2.2rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 16px;
}

.vision-content {
    position: relative;
    z-index: 2;
}

.vision-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.vision-goals {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.goal-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    transition: all 0.3s ease;
}

.goal-item:hover {
    background: rgba(0, 163, 255, 0.1);
    border-color: rgba(0, 163, 255, 0.3);
    transform: translateY(-4px);
}

.goal-icon {
    font-size: 1.5rem;
}

.goal-text {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    white-space: nowrap;
}

/* Responsive Design - Large Desktop */
@media (min-width: 1400px) {
    .about-section-modern {
        padding: 120px 0;
    }

    .about-title {
        font-size: 3.5rem;
    }

    .about-lead-modern {
        font-size: 1.4rem;
    }

    .about-cards-grid {
        gap: 80px;
    }

    .about-values-modern,
    .about-mission-modern {
        padding: 60px 50px;
    }

    .vision-container {
        padding: 80px 60px;
    }

    .vision-title {
        font-size: 2.5rem;
    }

    .vision-text {
        font-size: 1.3rem;
    }
}

/* Responsive Design - Tablet */
@media (max-width: 1024px) {
    .about-section-modern {
        padding: 80px 0;
    }

    .container.about-flex {
        padding: 0 30px;
    }

    .about-intro {
        margin-bottom: 60px;
    }

    .about-title {
        font-size: 2.5rem;
    }

    .about-lead-modern {
        font-size: 1.2rem;
    }

    .about-desc-modern {
        font-size: 1rem;
    }

    .about-cards-grid {
        gap: 40px;
        margin-bottom: 60px;
    }

    .about-values-modern,
    .about-mission-modern {
        padding: 40px 30px;
    }

    .values-title,
    .mission-title {
        font-size: 1.6rem;
        margin-bottom: 24px;
    }

    .value-modern {
        padding: 16px;
        gap: 16px;
    }

    .value-modern div h4 {
        font-size: 1rem;
    }

    .value-modern div p {
        font-size: 0.9rem;
    }

    .mission-text {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 25px;
    }

    .stat-card {
        padding: 30px 20px;
    }

    .stat-number {
        font-size: 2.2rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    .about-vision-modern {
        margin-top: 60px;
    }

    .vision-container {
        padding: 50px 40px;
    }

    .vision-title {
        font-size: 2rem;
    }

    .vision-text {
        font-size: 1.1rem;
    }

    .vision-goals {
        gap: 30px;
    }

    .goal-item {
        padding: 14px 20px;
    }

    .goal-text {
        font-size: 0.9rem;
    }
}

/* Responsive Design - Mobile */
@media (max-width: 768px) {
    .about-section-modern {
        padding: 60px 0;
    }

    .container.about-flex {
        padding: 0 20px;
    }

    .about-intro {
        margin-bottom: 50px;
    }

    .about-title {
        font-size: 2.2rem;
        margin-bottom: 20px;
    }

    .about-title::after {
        width: 60px;
        bottom: -8px;
    }

    .about-lead-modern {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 16px;
    }

    .about-desc-modern {
        font-size: 0.95rem;
        line-height: 1.7;
    }

    .about-cards-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        margin-bottom: 50px;
    }

    .about-values-modern,
    .about-mission-modern {
        padding: 35px 25px;
        border-radius: 20px;
    }

    .values-title,
    .mission-title {
        font-size: 1.4rem;
        margin-bottom: 20px;
        text-align: center;
    }

    .values-title::after,
    .mission-title::after {
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
    }

    .values-list {
        gap: 20px;
    }

    .value-modern {
        padding: 16px;
        gap: 14px;
        border-radius: 12px;
    }

    .value-modern:hover {
        transform: translateY(-4px);
    }

    .value-modern div h4 {
        font-size: 0.95rem;
        margin-bottom: 6px;
    }

    .value-modern div p {
        font-size: 0.85rem;
        line-height: 1.5;
    }

    .mission-content {
        text-align: center;
    }

    .mission-text {
        font-size: 0.95rem;
        line-height: 1.7;
    }

    .mission-text::before,
    .mission-text::after {
        font-size: 1.5rem;
        top: -8px;
    }

    .mission-text::before {
        left: -15px;
    }

    .mission-text::after {
        right: -15px;
    }

    .about-stats-modern {
        margin-top: 50px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }

    .stat-card {
        padding: 25px 20px;
        border-radius: 16px;
    }

    .stat-number {
        font-size: 2rem;
        margin-bottom: 6px;
    }

    .stat-label {
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }

    .about-vision-modern {
        margin-top: 50px;
    }

    .vision-container {
        padding: 40px 30px;
        border-radius: 20px;
    }

    .vision-title {
        font-size: 1.8rem;
        margin-bottom: 12px;
    }

    .vision-text {
        font-size: 1rem;
        line-height: 1.7;
        margin-bottom: 30px;
    }

    .vision-goals {
        flex-direction: column;
        gap: 16px;
        align-items: center;
    }

    .goal-item {
        padding: 12px 20px;
        border-radius: 25px;
    }

    .goal-icon {
        font-size: 1.3rem;
    }

    .goal-text {
        font-size: 0.85rem;
        white-space: normal;
        text-align: center;
    }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
    .about-section-modern {
        padding: 50px 0;
    }

    .container.about-flex {
        padding: 0 15px;
    }

    .about-intro {
        margin-bottom: 40px;
    }

    .about-title {
        font-size: 1.9rem;
        line-height: 1.3;
    }

    .about-lead-modern {
        font-size: 1rem;
        margin-bottom: 14px;
    }

    .about-desc-modern {
        font-size: 0.9rem;
    }

    .about-cards-grid {
        gap: 25px;
        margin-bottom: 40px;
    }

    .about-values-modern,
    .about-mission-modern {
        padding: 30px 20px;
    }

    .values-title,
    .mission-title {
        font-size: 1.3rem;
        margin-bottom: 18px;
    }

    .value-modern {
        padding: 14px;
        gap: 12px;
    }

    .value-modern div h4 {
        font-size: 0.9rem;
    }

    .value-modern div p {
        font-size: 0.8rem;
    }

    .mission-text {
        font-size: 0.9rem;
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }

    .stat-card {
        padding: 20px 15px;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .vision-container {
        padding: 35px 25px;
    }

    .vision-title {
        font-size: 1.6rem;
    }

    .vision-text {
        font-size: 0.95rem;
        margin-bottom: 25px;
    }

    .goal-item {
        padding: 10px 16px;
        width: 100%;
        justify-content: center;
    }

    .goal-text {
        font-size: 0.8rem;
    }
}

/* RTL Support */
.rtl .about-intro,
.rtl .mission-content,
.rtl .vision-container {
    text-align: center;
}

.rtl .about-title::after,
.rtl .values-title::after,
.rtl .mission-title::after {
    left: 50%;
    transform: translateX(-50%);
}

.rtl .value-modern:hover {
    transform: translateX(-8px);
}

.rtl .value-modern::before {
    left: auto;
    right: 0;
}

.rtl .mission-text::before {
    left: auto;
    right: -20px;
}

.rtl .mission-text::after {
    right: auto;
    left: -20px;
}

@media (max-width: 768px) {

    .rtl .values-title::after,
    .rtl .mission-title::after {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
    }

    .rtl .value-modern:hover {
        transform: translateY(-4px);
    }

    .rtl .mission-text::before {
        right: -15px;
    }

    .rtl .mission-text::after {
        left: -15px;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .about-section-modern {
        background: #000;
    }

    .about-values-modern,
    .about-mission-modern,
    .stat-card,
    .vision-container {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--primary);
    }

    .about-lead-modern,
    .about-desc-modern,
    .value-modern div p,
    .mission-text,
    .vision-text {
        color: #fff;
    }

    .goal-item {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--primary);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {

    .about-section-modern *,
    .about-section-modern *::before,
    .about-section-modern *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}