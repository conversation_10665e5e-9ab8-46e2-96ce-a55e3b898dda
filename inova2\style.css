/*
Theme Name: INOVA Energy
Description: A modern, multilingual WordPress theme for INOVA Energy with Persian/English language support and RTL/LTR layouts
Version: 1.0
Author: INOVA Energy Team
Text Domain: inova-energy
Domain Path: /languages
*/

/* Base Styles & Variables */
:root {
    --primary: #00A3FF;
    --primary-rgb: 0, 163, 255;
    --accent: #00FFD1;
    --accent-rgb: 0, 255, 209;
    --secondary: #001F3F;
    --text-light: #FFFFFF;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --transition: all 0.3s ease;
}

/* Reset & Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Vazirmatn', sans-serif;
    line-height: 1.6;
    background: var(--secondary);
    color: var(--text-light);
    overflow-x: hidden;
}

/* RTL Support */
body.rtl {
    direction: rtl;
    text-align: right;
}

body.ltr {
    direction: ltr;
    text-align: left;
}

/* English Font Support */
body.lang-en {
    font-family: 'Inter', 'Roboto', sans-serif;
}

/* Common Utility Classes */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
}

.neo-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Language Toggle Styles */
.lang-toggle {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 25px;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
}

.lang-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.lang-toggle .lang-icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.lang-toggle .lang-text {
    font-size: 14px;
    font-weight: 500;
}

/* WordPress Core Styles */
.wp-block-group {
    margin: 0;
}

.alignwide {
    width: 100%;
}

.alignfull {
    width: 100vw;
    margin-left: calc(50% - 50vw);
}

/* Screen Reader Text */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

/* Skip Link */
.skip-link {
    position: absolute;
    left: -9999px;
    top: 6px;
    z-index: 999999;
    text-decoration: none;
    color: var(--text-light);
    background: var(--primary);
    padding: 8px 16px;
    border-radius: 4px;
}

.skip-link:focus {
    left: 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    .lang-toggle {
        padding: 6px 12px;
    }
    
    .lang-toggle .lang-text {
        font-size: 12px;
    }
}
