/* Header Styles */
header {
    background-color: #ffffff; /* White background for the header */
    padding: 20px 0; /* Padding for top and bottom */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
    position: fixed; /* Fixed position to stay on top */
    width: 100%; /* Full width */
    z-index: 1000; /* Ensure it stays above other content */
    transition: background-color 0.3s ease; /* Smooth transition for background color */
}

header.scrolled {
    background-color: rgba(255, 255, 255, 0.9); /* Slightly transparent on scroll */
}

header .logo {
    font-size: 24px; /* Logo font size */
    font-weight: bold; /* Bold font for logo */
    color: #333; /* Dark color for logo */
    text-decoration: none; /* Remove underline */
}

nav {
    display: flex; /* Flexbox for navigation items */
    justify-content: space-around; /* Space between items */
    align-items: center; /* Center items vertically */
}

nav a {
    color: #333; /* Dark color for links */
    text-decoration: none; /* Remove underline */
    padding: 10px 15px; /* Padding for links */
    transition: color 0.3s ease; /* Smooth transition for color */
}

nav a:hover {
    color: #007BFF; /* Change color on hover */
}

@media (max-width: 768px) {
    nav {
        flex-direction: column; /* Stack items on smaller screens */
        align-items: flex-start; /* Align items to the start */
    }

    nav a {
        padding: 8px 10px; /* Adjust padding for smaller screens */
    }
}