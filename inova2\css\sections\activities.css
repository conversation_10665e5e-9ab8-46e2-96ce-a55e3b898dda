/* Activities Section Styles */

.activities-section {
    padding: 60px 20px;
    background-color: #f9f9f9;
    position: relative;
    overflow: hidden;
}

.activities-section h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 40px;
    color: #333;
    transition: color 0.3s ease;
}

.activities-section h2:hover {
    color: #007bff;
}

.activities-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.activity-item {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    width: calc(33.333% - 20px);
    max-width: 300px;
}

.activity-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.activity-item img {
    width: 100%;
    height: auto;
}

.activity-item .content {
    padding: 20px;
}

.activity-item .content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #007bff;
}

.activity-item .content p {
    font-size: 1rem;
    color: #666;
}

/* Smooth Scroll Transition */
html {
    scroll-behavior: smooth;
}

/* Media Queries */
@media (max-width: 768px) {
    .activity-item {
        width: calc(50% - 20px);
    }
}

@media (max-width: 480px) {
    .activity-item {
        width: 100%;
    }
}