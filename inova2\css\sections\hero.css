/* Hero Section Styles */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/hero-background.jpg') no-repeat center center/cover;
    color: #fff;
    text-align: center;
    overflow: hidden;
    transition: background 0.5s ease-in-out;
}

.hero h1 {
    font-size: 3rem;
    margin: 0;
    animation: fadeIn 1s ease forwards;
}

.hero p {
    font-size: 1.5rem;
    margin: 20px 0;
    animation: fadeIn 1.5s ease forwards;
}

.hero .cta-button {
    padding: 10px 20px;
    font-size: 1.2rem;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.hero .cta-button:hover {
    background-color: #0056b3;
}

/* Rebranding Section Styles */
.rebranding {
    padding: 60px 20px;
    background-color: #f8f9fa;
    text-align: center;
    transition: transform 0.5s ease;
}

.rebranding h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.rebranding p {
    font-size: 1.2rem;
    margin-bottom: 40px;
}

/* Activities Section Styles */
.activities {
    padding: 60px 20px;
    background-color: #ffffff;
    text-align: center;
    transition: transform 0.5s ease;
}

.activities h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.activities .activity-item {
    margin: 20px 0;
}

/* About Section Styles */
.about {
    padding: 60px 20px;
    background-color: #e9ecef;
    text-align: center;
    transition: transform 0.5s ease;
}

.about h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.about p {
    font-size: 1.2rem;
    margin-bottom: 40px;
}

/* Smooth Scroll Transition */
html {
    scroll-behavior: smooth;
}

/* Keyframes for Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}